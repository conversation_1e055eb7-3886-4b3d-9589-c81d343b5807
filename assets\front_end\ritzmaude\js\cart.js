/**
 * Ritz Maude Shopping Cart JavaScript
 * Handles cart functionality including add, remove, update, and display
 */

$(document).ready(function() {
    // Initialize cart functionality
    initializeCart();
    
    // Load cart data on cart page
    if (window.location.pathname.includes('/cart')) {
        loadCartData();
    }
});

/**
 * Initialize cart functionality
 */
function initializeCart() {
    // Update cart count on page load
    updateCartCount();
    
    // Bind event handlers
    bindCartEvents();
}

/**
 * Bind cart-related event handlers
 */
function bindCartEvents() {
    // Add to cart buttons
    $(document).on('click', '.add-to-cart-btn', function(e) {
        e.preventDefault();
        
        const productId = $(this).data('product-id');
        const variantId = $(this).data('variant-id') || null;
        const quantity = $(this).data('quantity') || 1;
        
        addToCart(productId, variantId, quantity, $(this));
    });
    
    // Remove from cart buttons
    $(document).on('click', '.remove-from-cart', function(e) {
        e.preventDefault();
        
        const cartId = $(this).data('cart-id');
        removeFromCart(cartId);
    });
    
    // Update quantity
    $(document).on('change', '.cart-quantity-input', function() {
        const cartId = $(this).data('cart-id');
        const quantity = parseInt($(this).val());
        
        if (quantity > 0) {
            updateCartQuantity(cartId, quantity);
        }
    });
    
    // Checkout button
    $(document).on('click', '#checkout-btn', function(e) {
        e.preventDefault();
        proceedToCheckout();
    });
    
    // Promo code form
    $(document).on('submit', '#promo-form', function(e) {
        e.preventDefault();
        applyPromoCode();
    });
}

/**
 * Add product to cart
 */
function addToCart(productId, variantId, quantity, buttonElement) {
    const originalText = buttonElement.html();
    
    // Show loading state
    buttonElement.prop('disabled', true).html('<span class="loading-spinner"></span> Adding...');
    
    $.ajax({
        url: window.eShop.apiUrl + 'manage_cart',
        type: 'POST',
        data: {
            [window.eShop.csrfName]: window.eShop.csrfHash,
            product_id: productId,
            product_variant_id: variantId,
            qty: quantity,
            is_saved_for_later: 0
        },
        success: function(response) {
            if (response.error === false) {
                showCartNotification('Product added to cart successfully!', 'success');
                updateCartCount();
                
                // Show cart drawer if available
                if (typeof showCartDrawer === 'function') {
                    showCartDrawer();
                }
            } else {
                showCartNotification(response.message || 'Failed to add product to cart', 'error');
            }
        },
        error: function() {
            showCartNotification('An error occurred. Please try again.', 'error');
        },
        complete: function() {
            buttonElement.prop('disabled', false).html(originalText);
        }
    });
}

/**
 * Remove product from cart
 */
function removeFromCart(cartId) {
    if (!confirm('Are you sure you want to remove this item from your cart?')) {
        return;
    }
    
    $.ajax({
        url: window.eShop.apiUrl + 'remove_from_cart',
        type: 'POST',
        data: {
            [window.eShop.csrfName]: window.eShop.csrfHash,
            id: cartId
        },
        success: function(response) {
            if (response.error === false) {
                showCartNotification('Product removed from cart', 'success');
                updateCartCount();
                loadCartData(); // Refresh cart display
            } else {
                showCartNotification(response.message || 'Failed to remove product', 'error');
            }
        },
        error: function() {
            showCartNotification('An error occurred. Please try again.', 'error');
        }
    });
}

/**
 * Update cart item quantity
 */
function updateCartQuantity(cartId, quantity) {
    $.ajax({
        url: window.eShop.apiUrl + 'manage_cart',
        type: 'POST',
        data: {
            [window.eShop.csrfName]: window.eShop.csrfHash,
            id: cartId,
            qty: quantity
        },
        success: function(response) {
            if (response.error === false) {
                loadCartData(); // Refresh cart display
                updateCartCount();
            } else {
                showCartNotification(response.message || 'Failed to update quantity', 'error');
            }
        },
        error: function() {
            showCartNotification('An error occurred. Please try again.', 'error');
        }
    });
}

/**
 * Load cart data for cart page
 */
function loadCartData() {
    $('#cart-loading').show();
    $('#cart-empty').hide();
    $('#cart-content').hide();
    
    $.ajax({
        url: window.eShop.apiUrl + 'get_user_cart',
        type: 'POST',
        data: {
            [window.eShop.csrfName]: window.eShop.csrfHash
        },
        success: function(response) {
            if (response.error === false && response.data && response.data.length > 0) {
                displayCartItems(response.data);
                $('#cart-content').show();
                $('#cart-empty').hide();
            } else {
                $('#cart-empty').show();
                $('#cart-content').hide();
            }
        },
        error: function() {
            showCartNotification('Error loading cart data', 'error');
            $('#cart-empty').show();
            $('#cart-content').hide();
        },
        complete: function() {
            $('#cart-loading').hide();
        }
    });
}

/**
 * Display cart items
 */
function displayCartItems(cartItems) {
    const cartContainer = $('#cart-items');
    let cartHTML = '';
    let subtotal = 0;
    
    cartItems.forEach(function(item) {
        const itemTotal = parseFloat(item.price) * parseInt(item.qty);
        subtotal += itemTotal;
        
        cartHTML += `
            <div class="cart-item" data-cart-id="${item.id}">
                <div class="cart-item__media">
                    <img src="${window.eShop.baseUrl}${item.image}" alt="${item.name}" loading="lazy">
                </div>
                <div class="cart-item__details">
                    <h3 class="cart-item__name">
                        <a href="${window.eShop.baseUrl}products/details/${item.slug}">${item.name}</a>
                    </h3>
                    <div class="cart-item__price">₦${formatPrice(item.price)}</div>
                    <div class="cart-item__quantity">
                        <label for="quantity-${item.id}">Quantity:</label>
                        <input type="number" id="quantity-${item.id}" class="cart-quantity-input" 
                               data-cart-id="${item.id}" value="${item.qty}" min="1" max="10">
                    </div>
                    <div class="cart-item__total">₦${formatPrice(itemTotal)}</div>
                    <button class="cart-item__remove remove-from-cart" data-cart-id="${item.id}">
                        Remove
                    </button>
                </div>
            </div>
        `;
    });
    
    cartContainer.html(cartHTML);
    
    // Update summary
    $('#cart-item-count').text(cartItems.length);
    $('#cart-subtotal').text('₦' + formatPrice(subtotal));
    $('#cart-total').text('₦' + formatPrice(subtotal)); // Add shipping calculation later
}

/**
 * Update cart count in header
 */
function updateCartCount() {
    $.ajax({
        url: window.eShop.apiUrl + 'get_user_cart',
        type: 'POST',
        data: {
            [window.eShop.csrfName]: window.eShop.csrfHash
        },
        success: function(response) {
            if (response.error === false && response.data) {
                const totalItems = response.data.length;
                $('.cart-count').text(totalItems);
                $('.cart-count-bubble').text(totalItems).toggle(totalItems > 0);
            }
        }
    });
}

/**
 * Show cart notification
 */
function showCartNotification(message, type) {
    const notification = $(`
        <div class="cart-notification ${type}">
            <div class="cart-notification__content">
                <span class="cart-notification__message">${message}</span>
                <button class="cart-notification__close">&times;</button>
            </div>
        </div>
    `);
    
    $('body').append(notification);
    
    // Auto-hide after 3 seconds
    setTimeout(function() {
        notification.fadeOut(function() {
            $(this).remove();
        });
    }, 3000);
    
    // Manual close
    notification.find('.cart-notification__close').on('click', function() {
        notification.fadeOut(function() {
            $(this).remove();
        });
    });
}

/**
 * Format price for display
 */
function formatPrice(price) {
    return parseFloat(price).toLocaleString('en-NG', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    });
}

/**
 * Proceed to checkout
 */
function proceedToCheckout() {
    window.location.href = window.eShop.baseUrl + 'checkout';
}

/**
 * Apply promo code
 */
function applyPromoCode() {
    const promoCode = $('#promo-code').val().trim();
    
    if (!promoCode) {
        showCartNotification('Please enter a promo code', 'error');
        return;
    }
    
    $.ajax({
        url: window.eShop.apiUrl + 'validate_promo_code',
        type: 'POST',
        data: {
            [window.eShop.csrfName]: window.eShop.csrfHash,
            promo_code: promoCode
        },
        success: function(response) {
            if (response.error === false) {
                showCartNotification('Promo code applied successfully!', 'success');
                loadCartData(); // Refresh cart to show discount
            } else {
                showCartNotification(response.message || 'Invalid promo code', 'error');
            }
        },
        error: function() {
            showCartNotification('Error applying promo code', 'error');
        }
    });
}

// Export functions for global access
window.RitzMaude = window.RitzMaude || {};
window.RitzMaude.cart = {
    addToCart: addToCart,
    removeFromCart: removeFromCart,
    updateCartQuantity: updateCartQuantity,
    loadCartData: loadCartData,
    updateCartCount: updateCartCount
};
