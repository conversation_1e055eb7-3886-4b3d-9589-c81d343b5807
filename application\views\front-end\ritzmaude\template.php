<?php
defined('BASEPATH') or exit('No direct script access allowed');

// Get system settings and theme configuration
$primary_colour = (isset($web_settings['primary_color']) && !empty($web_settings['primary_color'])) ? $web_settings['primary_color'] : '#000000';
$secondary_colour = (isset($web_settings['secondary_color']) && !empty($web_settings['secondary_color'])) ? $web_settings['secondary_color'] : '#770000';
$font_color = (isset($web_settings['font_color']) && !empty($web_settings['font_color'])) ? $web_settings['font_color'] : '#121212';

// RTL support
$is_rtl = (isset($web_settings['is_rtl']) && $web_settings['is_rtl'] == 1) ? 'true' : 'false';
$data['is_rtl'] = $is_rtl;
?>
<!DOCTYPE html>
<html class="no-js" lang="<?= (isset($web_settings['language']) && !empty($web_settings['language'])) ? $web_settings['language'] : 'en' ?>" <?= ($is_rtl == 'true') ? 'dir="rtl"' : '' ?>>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="<?= $primary_colour ?>">
    
    <!-- SEO Meta Tags -->
    <title><?= isset($title) ? $title : 'Ritz Maude - Fashion Store' ?></title>
    <meta name="description" content="<?= isset($description) ? $description : 'At Ritz Maude we honour your uniqueness by crafting pieces that celebrate the male and female form' ?>">
    <meta name="keywords" content="<?= isset($keywords) ? $keywords : 'fashion, clothing, style, ritz maude' ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?= base_url('assets/front_end/Ritz Maude Frontpage/favicons/files-Untitled_design_2.png') ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:site_name" content="RITZ MAUDE">
    <meta property="og:title" content="<?= isset($title) ? $title : 'Ritz Maude' ?>">
    <meta property="og:type" content="website">
    <meta property="og:description" content="<?= isset($description) ? $description : 'At Ritz Maude we honour your uniqueness by crafting pieces that celebrate the male and female form' ?>">
    <meta property="og:url" content="<?= current_url() ?>">
    
    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?= isset($title) ? $title : 'Ritz Maude' ?>">
    <meta name="twitter:description" content="<?= isset($description) ? $description : 'At Ritz Maude we honour your uniqueness by crafting pieces that celebrate the male and female form' ?>">
    
    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    
    <!-- CSS Includes -->
    <?php $this->load->view('front-end/' . THEME . '/include-css', $data); ?>
    
    <!-- Custom CSS Variables -->
    <style>
        :root {
            --primary-color: <?= $primary_colour ?>;
            --secondary-color: <?= $secondary_colour ?>;
            --font-color: <?= $font_color ?>;
            --color-background: 255,255,255;
            --color-foreground: 18,18,18;
            --font-body-family: "Century Gothic", sans-serif;
            --font-heading-family: "Playfair Display", serif;
        }
        
        * {
            --primary-color: <?= $primary_colour ?>;
            --secondary-color: <?= $secondary_colour ?>;
            --font-color: <?= $font_color ?>;
        }
        
        body {
            font-family: var(--font-body-family);
            color: rgba(var(--color-foreground), 0.75);
            background-color: rgb(var(--color-background));
        }
    </style>
    
    <!-- CSRF Token for AJAX requests -->
    <script>
        window.csrfName = '<?= $this->security->get_csrf_token_name() ?>';
        window.csrfHash = '<?= $this->security->get_csrf_hash() ?>';
        window.baseUrl = '<?= base_url() ?>';
        window.isLoggedIn = <?= isset($is_logged_in) ? ($is_logged_in ? 'true' : 'false') : 'false' ?>;
    </script>
</head>

<body class="gradient" data-is-rtl='<?= $is_rtl ?>'>
    <!-- Skip to content link for accessibility -->
    <a class="skip-to-content-link button visually-hidden" href="#MainContent">
        Skip to content
    </a>
    
    <!-- Header -->
    <?php $this->load->view('front-end/' . THEME . '/header'); ?>

    <!-- Main Content -->
    <main id="MainContent" class="content-for-layout focus-none" role="main" tabindex="-1">
        <?php $this->load->view('front-end/' . THEME . '/pages/' . $main_page); ?>
    </main>

    <!-- Footer -->
    <?php $this->load->view('front-end/' . THEME . '/footer'); ?>
    
    <!-- JavaScript Includes -->
    <?php $this->load->view('front-end/' . THEME . '/include-script'); ?>
    
    <!-- Custom JavaScript for dynamic functionality -->
    <script>
        // Update CSRF token after AJAX requests
        function updateCSRFToken() {
            $.ajaxSetup({
                data: {
                    [window.csrfName]: window.csrfHash
                }
            });
        }
        
        // Initialize CSRF token for all AJAX requests
        $(document).ready(function() {
            updateCSRFToken();
        });
        
        // Update CSRF token after each AJAX request
        $(document).ajaxComplete(function(event, xhr, settings) {
            if (xhr.responseJSON && xhr.responseJSON.csrfHash) {
                window.csrfHash = xhr.responseJSON.csrfHash;
                updateCSRFToken();
            }
        });
    </script>
</body>
</html>
