<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Customer_auth extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->database();
        $this->load->library(['ion_auth', 'form_validation', 'session']);
        $this->load->helper(['url', 'language']);
        $this->load->model('Auth_model');
        
        $this->data['web_settings'] = get_settings('web_settings', true);
        $this->data['system_settings'] = get_settings('system_settings', true);
        $this->response['csrfName'] = $this->security->get_csrf_token_name();
        $this->response['csrfHash'] = $this->security->get_csrf_hash();
    }

    public function login()
    {
        // If already logged in, redirect to account page
        if ($this->ion_auth->logged_in()) {
            redirect('my-account', 'refresh');
        }

        $this->data['main_page'] = 'login';
        $this->data['title'] = 'Login | ' . $this->data['web_settings']['site_title'];
        $this->data['keywords'] = 'Login, ' . $this->data['web_settings']['meta_keywords'];
        $this->data['description'] = 'Login | ' . $this->data['web_settings']['meta_description'];
        
        $this->load->view('front-end/' . THEME . '/template', $this->data);
    }

    public function register()
    {
        // If already logged in, redirect to account page
        if ($this->ion_auth->logged_in()) {
            redirect('my-account', 'refresh');
        }

        $this->data['main_page'] = 'login'; // Use same page with tabs
        $this->data['title'] = 'Register | ' . $this->data['web_settings']['site_title'];
        $this->data['keywords'] = 'Register, ' . $this->data['web_settings']['meta_keywords'];
        $this->data['description'] = 'Register | ' . $this->data['web_settings']['meta_description'];
        
        $this->load->view('front-end/' . THEME . '/template', $this->data);
    }

    public function authenticate()
    {
        $this->form_validation->set_rules('identity', 'Email/Mobile', 'trim|required|xss_clean');
        $this->form_validation->set_rules('password', 'Password', 'trim|required|xss_clean');

        if (!$this->form_validation->run()) {
            $this->response['error'] = true;
            $this->response['message'] = validation_errors();
            echo json_encode($this->response);
            return;
        }

        $identity = $this->input->post('identity', true);
        $password = $this->input->post('password', true);
        $remember = (bool)$this->input->post('remember');

        // Determine if identity is email or mobile
        $identity_column = $this->config->item('identity', 'ion_auth');
        
        // Check if user exists
        $user = $this->db->select('id, email, mobile, active')
                         ->where('email', $identity)
                         ->or_where('mobile', $identity)
                         ->get('users')
                         ->row();

        if (!$user) {
            $this->response['error'] = true;
            $this->response['message'] = 'Invalid email/mobile or password.';
            echo json_encode($this->response);
            return;
        }

        if ($user->active == 0) {
            $this->response['error'] = true;
            $this->response['message'] = 'Your account is not activated. Please check your email for activation link.';
            echo json_encode($this->response);
            return;
        }

        // Use the appropriate identity for Ion Auth login
        $login_identity = ($identity_column == 'mobile') ? $user->mobile : $user->email;
        
        if ($this->ion_auth->login($login_identity, $password, $remember)) {
            // Check if user is in customer group
            if ($this->ion_auth->in_group('customers', $user->id)) {
                $this->response['error'] = false;
                $this->response['message'] = 'Login successful!';
                $this->response['redirect'] = $this->input->get('redirect') ? base_url($this->input->get('redirect')) : base_url('my-account');
            } else {
                $this->ion_auth->logout();
                $this->response['error'] = true;
                $this->response['message'] = 'Access denied. Customer account required.';
            }
        } else {
            $this->response['error'] = true;
            $this->response['message'] = 'Invalid email/mobile or password.';
        }

        echo json_encode($this->response);
    }

    public function create_account()
    {
        $this->form_validation->set_rules('first_name', 'First Name', 'trim|required|xss_clean');
        $this->form_validation->set_rules('last_name', 'Last Name', 'trim|required|xss_clean');
        $this->form_validation->set_rules('email', 'Email', 'trim|required|valid_email|xss_clean|is_unique[users.email]');
        $this->form_validation->set_rules('mobile', 'Mobile', 'trim|required|numeric|xss_clean|is_unique[users.mobile]');
        $this->form_validation->set_rules('password', 'Password', 'trim|required|min_length[6]|xss_clean');
        $this->form_validation->set_rules('confirm_password', 'Confirm Password', 'trim|required|matches[password]|xss_clean');

        if (!$this->form_validation->run()) {
            $this->response['error'] = true;
            $this->response['message'] = validation_errors();
            echo json_encode($this->response);
            return;
        }

        $email = strtolower($this->input->post('email', true));
        $mobile = $this->input->post('mobile', true);
        $password = $this->input->post('password', true);
        $first_name = $this->input->post('first_name', true);
        $last_name = $this->input->post('last_name', true);

        // Determine identity based on Ion Auth config
        $identity_column = $this->config->item('identity', 'ion_auth');
        $identity = ($identity_column == 'mobile') ? $mobile : $email;

        $additional_data = [
            'first_name' => $first_name,
            'last_name' => $last_name,
            'username' => $first_name . ' ' . $last_name,
            'mobile' => $mobile,
            'image' => '',
            'balance' => '0.00',
            'activation_selector' => '',
            'activation_code' => '',
            'forgotten_password_selector' => '',
            'forgotten_password_code' => '',
            'forgotten_password_time' => null,
            'remember_selector' => '',
            'remember_code' => '',
            'created_on' => time(),
            'last_login' => null,
            'active' => 1, // Auto-activate for simplified registration
            'company' => '',
            'address' => '',
            'city' => '',
            'state' => '',
            'country' => '',
            'pincode' => '',
            'apikey' => '',
            'referral_code' => generate_referral_code(),
            'friends_code' => '',
            'fcm_id' => '',
            'latitude' => '',
            'longitude' => '',
            'created_at' => date('Y-m-d H:i:s')
        ];

        // Register user with customers group (group ID 2)
        $user_id = $this->ion_auth->register($identity, $password, $email, $additional_data, ['2']);

        if ($user_id) {
            // Generate API key for the user
            $api_key = generate_token($identity);
            $this->db->where('id', $user_id)->update('users', ['apikey' => $api_key]);

            $this->response['error'] = false;
            $this->response['message'] = 'Account created successfully! You can now login.';
            
            // Auto-login the user
            if ($this->ion_auth->login($identity, $password, false)) {
                $this->response['redirect'] = base_url('my-account');
            }
        } else {
            $this->response['error'] = true;
            $this->response['message'] = 'Failed to create account. Please try again.';
        }

        echo json_encode($this->response);
    }

    public function forgot_password()
    {
        $this->form_validation->set_rules('email', 'Email', 'trim|required|valid_email|xss_clean');

        if (!$this->form_validation->run()) {
            $this->response['error'] = true;
            $this->response['message'] = validation_errors();
            echo json_encode($this->response);
            return;
        }

        $email = $this->input->post('email', true);
        
        // Check if user exists
        $user = $this->db->select('id, email, mobile')
                         ->where('email', $email)
                         ->get('users')
                         ->row();

        if (!$user) {
            $this->response['error'] = true;
            $this->response['message'] = 'Email address not found.';
            echo json_encode($this->response);
            return;
        }

        // Use Ion Auth's forgot password functionality
        $identity_column = $this->config->item('identity', 'ion_auth');
        $identity = ($identity_column == 'mobile') ? $user->mobile : $user->email;
        
        if ($this->ion_auth->forgotten_password($identity)) {
            $this->response['error'] = false;
            $this->response['message'] = 'Password reset instructions have been sent to your email.';
        } else {
            $this->response['error'] = true;
            $this->response['message'] = 'Failed to send reset email. Please try again.';
        }

        echo json_encode($this->response);
    }

    public function logout()
    {
        $this->ion_auth->logout();
        redirect('/', 'refresh');
    }

    public function reset_password($code = null)
    {
        if (!$code) {
            show_404();
        }

        $user = $this->ion_auth->forgotten_password_check($code);

        if ($user) {
            $this->data['main_page'] = 'reset-password';
            $this->data['title'] = 'Reset Password | ' . $this->data['web_settings']['site_title'];
            $this->data['code'] = $code;
            $this->load->view('front-end/' . THEME . '/template', $this->data);
        } else {
            $this->session->set_flashdata('message', 'Invalid or expired reset link.');
            redirect('customer-auth/login', 'refresh');
        }
    }

    public function reset_password_complete()
    {
        $this->form_validation->set_rules('code', 'Reset Code', 'trim|required|xss_clean');
        $this->form_validation->set_rules('password', 'New Password', 'trim|required|min_length[6]|xss_clean');
        $this->form_validation->set_rules('confirm_password', 'Confirm Password', 'trim|required|matches[password]|xss_clean');

        if (!$this->form_validation->run()) {
            $this->response['error'] = true;
            $this->response['message'] = validation_errors();
            echo json_encode($this->response);
            return;
        }

        $code = $this->input->post('code', true);
        $password = $this->input->post('password', true);

        if ($this->ion_auth->forgotten_password_complete($code, $password)) {
            $this->response['error'] = false;
            $this->response['message'] = 'Password reset successfully! You can now login with your new password.';
            $this->response['redirect'] = base_url('customer-auth/login');
        } else {
            $this->response['error'] = true;
            $this->response['message'] = 'Failed to reset password. Please try again.';
        }

        echo json_encode($this->response);
    }
}

// Helper function to generate referral code
if (!function_exists('generate_referral_code')) {
    function generate_referral_code($length = 8) {
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }
}

// Helper function to generate API token
if (!function_exists('generate_token')) {
    function generate_token($identity) {
        return hash('sha256', $identity . time() . rand(1000, 9999));
    }
}
