<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<!-- Checkout Success Page Content -->

<!-- Success Header -->
<section class="shopify-section section">
    <style>
        .success-header {
            padding-top: 56px;
            padding-bottom: 44px;
            text-align: center;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }
        .success-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem auto;
            font-size: 2.5rem;
        }
        .success-header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .success-header p {
            font-size: 1.25rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
    </style>
    <div class="success-header">
        <div class="page-width">
            <div class="success-icon scroll-trigger animate--fade-in">
                ✓
            </div>
            <h1 class="scroll-trigger animate--slide-in">Order Placed Successfully!</h1>
            <p class="scroll-trigger animate--slide-in">Thank you for your purchase. Your order has been received and is being processed.</p>
        </div>
    </div>
</section>

<!-- Order Details -->
<section class="shopify-section section">
    <style>
        .section-order-padding {
            padding-top: 56px;
            padding-bottom: 44px;
        }
        .order-summary-card {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        .order-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        .order-info-item {
            text-align: center;
            padding: 1.5rem;
            background: rgba(var(--primary-color), 0.05);
            border-radius: 8px;
        }
        .order-info-item h3 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }
        .order-info-item p {
            margin: 0;
            font-weight: 600;
            font-size: 1.2rem;
        }
        .order-status {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.875rem;
        }
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        .status-confirmed {
            background: #d4edda;
            color: #155724;
        }
        .next-steps {
            background: rgba(var(--color-foreground), 0.02);
            padding: 2rem;
            border-radius: 8px;
            margin-top: 2rem;
        }
        .next-steps h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        .next-steps ul {
            list-style: none;
            padding: 0;
        }
        .next-steps li {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(var(--color-foreground), 0.1);
        }
        .next-steps li:last-child {
            border-bottom: none;
        }
        .next-steps li::before {
            content: '→';
            color: var(--primary-color);
            font-weight: bold;
            margin-right: 0.5rem;
        }
        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }
        .btn {
            background: var(--primary-color);
            color: white;
            padding: 1rem 2rem;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-block;
        }
        .btn:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        .btn--secondary {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }
        .btn--secondary:hover {
            background: var(--primary-color);
            color: white;
        }
    </style>
    
    <div class="color-scheme-1 gradient">
        <div class="page-width isolate section-order-padding">
            
            <!-- Order Summary Card -->
            <div class="order-summary-card scroll-trigger animate--slide-in">
                <h2 style="text-align: center; margin-bottom: 2rem; color: var(--primary-color);">Order Summary</h2>
                
                <div class="order-info-grid">
                    <div class="order-info-item">
                        <h3>Order Number</h3>
                        <p>#<?= isset($order['id']) ? $order['id'] : 'N/A' ?></p>
                    </div>
                    
                    <div class="order-info-item">
                        <h3>Order Date</h3>
                        <p><?= isset($order['date_added']) ? date('M d, Y', strtotime($order['date_added'])) : 'N/A' ?></p>
                    </div>
                    
                    <div class="order-info-item">
                        <h3>Total Amount</h3>
                        <p>₦<?= isset($order['final_total']) ? number_format($order['final_total'], 2) : '0.00' ?></p>
                    </div>
                    
                    <div class="order-info-item">
                        <h3>Payment Method</h3>
                        <p><?= isset($order['payment_method']) ? ucfirst(strtolower($order['payment_method'])) : 'N/A' ?></p>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 2rem;">
                    <h3>Order Status</h3>
                    <span class="order-status status-<?= isset($order['status']) ? $order['status'] : 'pending' ?>">
                        <?= isset($order['status']) ? ucfirst($order['status']) : 'Pending' ?>
                    </span>
                </div>
            </div>

            <!-- Shipping Information -->
            <?php if (isset($order['address']) && !empty($order['address'])): ?>
                <?php $address = json_decode($order['address'], true); ?>
                <div class="order-summary-card scroll-trigger animate--slide-in">
                    <h3 style="color: var(--primary-color); margin-bottom: 1.5rem;">Shipping Information</h3>
                    <div style="background: rgba(var(--color-foreground), 0.02); padding: 1.5rem; border-radius: 8px;">
                        <p><strong><?= $address['first_name'] ?? '' ?> <?= $address['last_name'] ?? '' ?></strong></p>
                        <p><?= $address['address'] ?? '' ?></p>
                        <p><?= $address['city'] ?? '' ?>, <?= $address['state'] ?? '' ?> <?= $address['pincode'] ?? '' ?></p>
                        <p>Phone: <?= $address['mobile'] ?? '' ?></p>
                        <p>Email: <?= $address['email'] ?? '' ?></p>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Next Steps -->
            <div class="next-steps scroll-trigger animate--slide-in">
                <h3>What happens next?</h3>
                <ul>
                    <?php if (isset($order['payment_method']) && strtolower($order['payment_method']) === 'cod'): ?>
                        <li>We'll process your order and prepare it for shipping</li>
                        <li>You'll receive a confirmation email with tracking details</li>
                        <li>Your order will be delivered within 3-5 business days</li>
                        <li>Pay the delivery agent when your order arrives</li>
                    <?php else: ?>
                        <li>We'll confirm your payment and process your order</li>
                        <li>You'll receive a confirmation email with order details</li>
                        <li>Your order will be prepared and shipped within 1-2 business days</li>
                        <li>Track your order status in your account dashboard</li>
                    <?php endif; ?>
                </ul>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons scroll-trigger animate--slide-in">
                <a href="<?= base_url('my-account/order/' . (isset($order['id']) ? $order['id'] : '')) ?>" class="btn">
                    View Order Details
                </a>
                <a href="<?= base_url('my-account') ?>" class="btn btn--secondary">
                    My Account
                </a>
                <a href="<?= base_url('products') ?>" class="btn btn--secondary">
                    Continue Shopping
                </a>
            </div>

            <!-- Contact Support -->
            <div style="text-align: center; margin-top: 3rem; padding: 2rem; background: rgba(var(--color-foreground), 0.02); border-radius: 8px;" class="scroll-trigger animate--slide-in">
                <h3 style="color: var(--primary-color); margin-bottom: 1rem;">Need Help?</h3>
                <p style="margin-bottom: 1.5rem; color: rgba(var(--color-foreground), 0.7);">
                    If you have any questions about your order, our customer support team is here to help.
                </p>
                <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                    <a href="<?= base_url('contact') ?>" class="btn btn--secondary">Contact Support</a>
                    <a href="mailto:<?= isset($web_settings['support_email']) ? $web_settings['support_email'] : '<EMAIL>' ?>" class="btn btn--secondary">Email Us</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Order Tracking Script -->
<script>
$(document).ready(function() {
    if (typeof initializeDynamicContent === 'function') {
        initializeDynamicContent();
    }
    
    // Auto-refresh order status every 30 seconds
    var orderId = <?= isset($order['id']) ? $order['id'] : 'null' ?>;
    if (orderId) {
        setInterval(function() {
            checkOrderStatus(orderId);
        }, 30000);
    }
});

function checkOrderStatus(orderId) {
    $.ajax({
        url: window.eShop.baseUrl + 'my-account/order-status',
        type: 'POST',
        data: {
            [window.eShop.csrfName]: window.eShop.csrfHash,
            order_id: orderId
        },
        dataType: 'json',
        success: function(response) {
            if (response.error === false && response.data) {
                var currentStatus = $('.order-status').text().toLowerCase().trim();
                var newStatus = response.data.status.toLowerCase();
                
                if (currentStatus !== newStatus) {
                    // Update status display
                    $('.order-status')
                        .removeClass('status-' + currentStatus)
                        .addClass('status-' + newStatus)
                        .text(response.data.status.charAt(0).toUpperCase() + response.data.status.slice(1));
                    
                    // Show notification
                    if (window.eShop && window.eShop.cart && window.eShop.cart.showNotification) {
                        window.eShop.cart.showNotification('Order status updated to: ' + response.data.status, 'info');
                    }
                }
            }
        }
    });
}
</script>
