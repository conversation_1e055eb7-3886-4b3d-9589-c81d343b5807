/**
 * Ritz Maude Product Listing JavaScript
 * Handles product filtering, sorting, and dynamic loading
 */

$(document).ready(function() {
    // Initialize product listing functionality
    initializeProductListing();
    
    // Load products on page load
    loadProducts();
});

/**
 * Initialize product listing functionality
 */
function initializeProductListing() {
    // Bind event handlers
    bindProductListingEvents();
    
    // Initialize filters
    initializeFilters();
}

/**
 * Bind product listing event handlers
 */
function bindProductListingEvents() {
    // Sort dropdown change
    $(document).on('change', '#SortBy', function() {
        const sortValue = $(this).val();
        loadProducts({ sort: sortValue });
    });
    
    // Filter checkboxes
    $(document).on('change', '.product_attributes', function() {
        applyFilters();
    });
    
    // Brand filters
    $(document).on('change', '.brand', function() {
        applyFilters();
    });
    
    // Clear filters
    $(document).on('click', '.clear-filters', function(e) {
        e.preventDefault();
        clearAllFilters();
    });
    
    // Filter toggle for mobile
    $(document).on('click', '.facets__disclosure summary', function(e) {
        const details = $(this).closest('details');
        setTimeout(() => {
            if (details.attr('open')) {
                details.addClass('open');
            } else {
                details.removeClass('open');
            }
        }, 10);
    });
}

/**
 * Initialize filters from URL parameters
 */
function initializeFilters() {
    const urlParams = new URLSearchParams(window.location.search);
    
    // Set sort dropdown
    const sort = urlParams.get('sort');
    if (sort) {
        $('#SortBy').val(sort);
    }
    
    // Set filter checkboxes
    urlParams.forEach((value, key) => {
        if (key.startsWith('filter-')) {
            const values = value.split('|');
            values.forEach(val => {
                $(`.product_attributes[value="${val}"]`).prop('checked', true);
            });
        }
    });
    
    // Set brand filter
    const brand = urlParams.get('brand');
    if (brand) {
        $(`.brand[value="${brand}"]`).prop('checked', true);
    }
}

/**
 * Load products with filters and sorting
 */
function loadProducts(options = {}) {
    const productGrid = $('#product-grid');
    const loadingContainer = $('#products-loading');
    const noProductsContainer = $('#no-products');
    
    // Show loading state
    productGrid.hide();
    noProductsContainer.hide();
    loadingContainer.show();
    
    // Collect filter parameters
    const params = collectFilterParams();
    
    // Add additional options
    Object.assign(params, options);
    
    // Make API call
    $.ajax({
        url: window.eShop.apiUrl + 'get_products',
        type: 'POST',
        data: {
            [window.eShop.csrfName]: window.eShop.csrfHash,
            ...params,
            limit: 20,
            offset: 0
        },
        dataType: 'json',
        success: function(response) {
            if (response.error === false && response.data && response.data.length > 0) {
                displayProducts(response.data);
                updateProductCount(response.data.length);
                productGrid.show();
            } else {
                noProductsContainer.show();
                updateProductCount(0);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading products:', error);
            noProductsContainer.show();
            updateProductCount(0);
        },
        complete: function() {
            loadingContainer.hide();
        }
    });
}

/**
 * Collect filter parameters from form
 */
function collectFilterParams() {
    const params = {};
    
    // Collect attribute filters
    $('.product_attributes:checked').each(function() {
        const attribute = $(this).data('attribute');
        const value = $(this).val();
        
        if (!params[`filter-${attribute}`]) {
            params[`filter-${attribute}`] = [];
        }
        params[`filter-${attribute}`].push(value);
    });
    
    // Convert arrays to pipe-separated strings
    Object.keys(params).forEach(key => {
        if (Array.isArray(params[key])) {
            params[key] = params[key].join('|');
        }
    });
    
    // Collect brand filter
    const selectedBrand = $('.brand:checked').val();
    if (selectedBrand) {
        params.brand = selectedBrand;
    }
    
    // Collect sort option
    const sortBy = $('#SortBy').val();
    if (sortBy && sortBy !== 'manual') {
        params.sort = sortBy;
    }
    
    return params;
}

/**
 * Display products in the grid
 */
function displayProducts(products) {
    const productGrid = $('#product-grid');
    let productsHTML = '';
    
    products.forEach(function(product, index) {
        const animationOrder = index + 1;
        const salePrice = product.sale_price && parseFloat(product.sale_price) > 0 ? parseFloat(product.sale_price) : null;
        const regularPrice = parseFloat(product.price);
        const hasDiscount = salePrice && salePrice < regularPrice;
        
        productsHTML += `
            <li class="grid__item scroll-trigger animate--slide-in" data-cascade style="--animation-order: ${animationOrder};">
                <div class="card-wrapper product-card-wrapper underline-links-hover">
                    <div class="card card--standard card--media" style="--ratio-percent: 125.0%;">
                        <div class="card__inner color-scheme-1 gradient ratio" style="--ratio-percent: 125.0%;">
                            <div class="card__media">
                                <div class="media media--transparent media--hover-effect">
                                    <img src="${window.eShop.baseUrl}${product.image}" 
                                         alt="${product.name}" 
                                         class="motion-reduce"
                                         loading="lazy"
                                         style="object-fit: cover; width: 100%; height: 100%;">
                                </div>
                            </div>
                            <div class="card__content">
                                <div class="card__information">
                                    <h3 class="card__heading">
                                        <a href="${window.eShop.baseUrl}products/details/${product.slug}" class="full-unstyled-link">
                                            ${product.name}
                                        </a>
                                    </h3>
                                    <div class="card-information">
                                        <span class="caption-large light">${product.short_description || ''}</span>
                                        <div class="price">
                                            ${hasDiscount ? 
                                                `<div class="price__container">
                                                    <span class="price-item price-item--sale price-item--last">
                                                        ₦${formatPrice(salePrice)}
                                                    </span>
                                                    <span class="price-item price-item--regular">
                                                        ₦${formatPrice(regularPrice)}
                                                    </span>
                                                </div>` :
                                                `<div class="price__container">
                                                    <span class="price-item price-item--regular">
                                                        ₦${formatPrice(regularPrice)}
                                                    </span>
                                                </div>`
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="card__badge bottom left">
                                    ${hasDiscount ? '<span class="badge badge--bottom-left color-scheme-4">Sale</span>' : ''}
                                </div>
                            </div>
                        </div>
                        <div class="card__content">
                            <div class="card__information">
                                <div class="card-information">
                                    <div class="product-form">
                                        <div class="product-form__buttons">
                                            <button type="button" 
                                                    class="product-form__cart-submit button button--full-width button--secondary add-to-cart-btn"
                                                    data-product-id="${product.id}"
                                                    data-quantity="1">
                                                <span>Add to cart</span>
                                                <div class="loading-overlay__spinner hidden">
                                                    <svg aria-hidden="true" focusable="false" class="spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg">
                                                        <circle class="path" fill="none" stroke-width="6" stroke-linecap="round" cx="33" cy="33" r="30"></circle>
                                                    </svg>
                                                </div>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </li>
        `;
    });
    
    productGrid.html(productsHTML);
}

/**
 * Apply filters and reload products
 */
function applyFilters() {
    // Update URL with current filters
    updateURL();
    
    // Reload products with new filters
    loadProducts();
}

/**
 * Clear all filters
 */
function clearAllFilters() {
    // Uncheck all filter checkboxes
    $('.product_attributes, .brand').prop('checked', false);
    
    // Reset sort dropdown
    $('#SortBy').val('manual');
    
    // Clear URL parameters
    window.history.pushState({}, '', window.location.pathname);
    
    // Reload products
    loadProducts();
}

/**
 * Update URL with current filter parameters
 */
function updateURL() {
    const params = collectFilterParams();
    const urlParams = new URLSearchParams();
    
    Object.keys(params).forEach(key => {
        if (params[key]) {
            urlParams.set(key, params[key]);
        }
    });
    
    const newURL = window.location.pathname + (urlParams.toString() ? '?' + urlParams.toString() : '');
    window.history.pushState({}, '', newURL);
}

/**
 * Update product count display
 */
function updateProductCount(count) {
    $('#ProductCountDesktop').text(`${count} product${count !== 1 ? 's' : ''}`);
}

/**
 * Format price for display
 */
function formatPrice(price) {
    return parseFloat(price).toLocaleString('en-NG', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    });
}

// Export functions for global access
window.RitzMaude = window.RitzMaude || {};
window.RitzMaude.productListing = {
    loadProducts: loadProducts,
    applyFilters: applyFilters,
    clearAllFilters: clearAllFilters
};
