/* Cart Notifications */
.cart-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.cart-notification.show {
    transform: translateX(0);
}

.cart-notification--success {
    background: #28a745;
}

.cart-notification--error {
    background: #dc3545;
}

.cart-notification--info {
    background: #17a2b8;
}

.cart-notification--warning {
    background: #ffc107;
    color: #212529;
}

/* Cart Icon Styling */
.cart-icon {
    position: relative;
    display: inline-block;
    transition: transform 0.2s ease;
}

.cart-icon:hover {
    transform: scale(1.05);
}

.cart-icon.has-items {
    animation: cartBounce 0.6s ease;
}

@keyframes cartBounce {
    0%, 20%, 60%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    80% {
        transform: translateY(-5px);
    }
}

.cart-count-bubble {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    line-height: 1;
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-container {
    text-align: center;
    padding: 2rem;
}

.loading-container .loading-spinner {
    width: 32px;
    height: 32px;
    border-width: 3px;
    margin-bottom: 1rem;
}

/* Product Cards */
.product-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.product-card img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover img {
    transform: scale(1.05);
}

.product-card h3 {
    padding: 1rem 1rem 0.5rem;
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    line-height: 1.3;
}

.product-card .price {
    padding: 0 1rem;
    margin: 0;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color, #007bff);
}

.product-card .add-to-cart-btn {
    margin: 1rem;
    margin-top: auto;
    padding: 0.75rem;
    background: var(--primary-color, #007bff);
    color: white;
    border: none;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.product-card .add-to-cart-btn:hover {
    background: var(--secondary-color, #0056b3);
}

.product-card .add-to-cart-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

/* Grid Layouts */
.product-grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

@media (max-width: 768px) {
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .product-grid {
        grid-template-columns: 1fr 1fr;
        gap: 0.75rem;
    }
    
    .product-card h3 {
        font-size: 0.9rem;
        padding: 0.75rem 0.75rem 0.25rem;
    }
    
    .product-card .price {
        font-size: 1rem;
        padding: 0 0.75rem;
    }
    
    .product-card .add-to-cart-btn {
        margin: 0.75rem;
        padding: 0.5rem;
        font-size: 0.9rem;
    }
}

/* Form Styling */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-input,
.form-select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-input:focus,
.form-select:focus {
    outline: none;
    border-color: var(--primary-color, #007bff);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-input.error {
    border-color: #dc3545;
}

.form-input.error:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

/* Button Styling */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: var(--primary-color, #007bff);
    color: white;
    text-decoration: none;
    border: none;
    border-radius: 4px;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    line-height: 1.5;
}

.btn:hover {
    background: var(--secondary-color, #0056b3);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn--secondary {
    background: transparent;
    color: var(--primary-color, #007bff);
    border: 2px solid var(--primary-color, #007bff);
}

.btn--secondary:hover {
    background: var(--primary-color, #007bff);
    color: white;
}

.btn--full-width {
    width: 100%;
}

/* Responsive Notifications */
@media (max-width: 480px) {
    .cart-notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100%);
    }
    
    .cart-notification.show {
        transform: translateY(0);
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.5s ease;
}

@keyframes slideIn {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }
