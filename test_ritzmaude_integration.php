<?php
/**
 * Ritz Maude Theme Integration Test Script
 * This script tests all the integrated functionality
 */

// Include CodeIgniter bootstrap
require_once 'index.php';

class RitzMaudeIntegrationTest
{
    private $ci;
    private $test_results = [];
    
    public function __construct()
    {
        $this->ci =& get_instance();
        $this->ci->load->database();
        $this->ci->load->library(['ion_auth', 'session']);
        $this->ci->load->model(['Cart_model', 'Order_model']);
    }
    
    public function runAllTests()
    {
        echo "<h1>Ritz Maude Theme Integration Test Results</h1>";
        echo "<style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .test-pass { color: green; font-weight: bold; }
            .test-fail { color: red; font-weight: bold; }
            .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
            .test-item { margin: 10px 0; padding: 10px; background: #f9f9f9; border-radius: 3px; }
        </style>";
        
        $this->testDatabaseConnection();
        $this->testThemeConfiguration();
        $this->testControllers();
        $this->testViews();
        $this->testAPIEndpoints();
        $this->testJavaScriptFiles();
        $this->testCSSFiles();
        
        $this->displaySummary();
    }
    
    private function testDatabaseConnection()
    {
        echo "<div class='test-section'>";
        echo "<h2>Database Connection Tests</h2>";
        
        // Test database connection
        $this->runTest('Database Connection', function() {
            return $this->ci->db->conn_id !== false;
        });
        
        // Test themes table
        $this->runTest('Themes Table Exists', function() {
            return $this->ci->db->table_exists('themes');
        });
        
        // Test Ritz Maude theme in database
        $this->runTest('Ritz Maude Theme in Database', function() {
            $theme = $this->ci->db->where('slug', 'ritzmaude')->get('themes')->row();
            return $theme && $theme->is_default == 1;
        });
        
        // Test required tables
        $required_tables = ['users', 'products', 'categories', 'cart', 'orders', 'order_items'];
        foreach ($required_tables as $table) {
            $this->runTest("Table '$table' exists", function() use ($table) {
                return $this->ci->db->table_exists($table);
            });
        }
        
        echo "</div>";
    }
    
    private function testThemeConfiguration()
    {
        echo "<div class='test-section'>";
        echo "<h2>Theme Configuration Tests</h2>";
        
        // Test theme constant
        $this->runTest('THEME Constant Defined', function() {
            return defined('THEME') && THEME === 'ritzmaude';
        });
        
        // Test theme directory exists
        $this->runTest('Theme Directory Exists', function() {
            return is_dir(APPPATH . 'views/front-end/ritzmaude');
        });
        
        // Test required theme files
        $required_files = [
            'template.php',
            'header.php',
            'footer.php',
            'include-css.php',
            'include-script.php'
        ];
        
        foreach ($required_files as $file) {
            $this->runTest("Theme file '$file' exists", function() use ($file) {
                return file_exists(APPPATH . 'views/front-end/ritzmaude/' . $file);
            });
        }
        
        // Test page files
        $page_files = [
            'pages/home.php',
            'pages/products.php',
            'pages/product-details.php',
            'pages/cart.php',
            'pages/checkout.php',
            'pages/login.php',
            'pages/my-account.php',
            'pages/about.php'
        ];
        
        foreach ($page_files as $file) {
            $this->runTest("Page file '$file' exists", function() use ($file) {
                return file_exists(APPPATH . 'views/front-end/ritzmaude/' . $file);
            });
        }
        
        echo "</div>";
    }
    
    private function testControllers()
    {
        echo "<div class='test-section'>";
        echo "<h2>Controller Tests</h2>";
        
        // Test custom controllers exist
        $controllers = [
            'Customer_auth.php',
            'Checkout.php'
        ];
        
        foreach ($controllers as $controller) {
            $this->runTest("Controller '$controller' exists", function() use ($controller) {
                return file_exists(APPPATH . 'controllers/' . $controller);
            });
        }
        
        // Test existing controllers
        $existing_controllers = [
            'Home.php',
            'Products.php',
            'Cart.php',
            'My_account.php'
        ];
        
        foreach ($existing_controllers as $controller) {
            $this->runTest("Existing controller '$controller' accessible", function() use ($controller) {
                return file_exists(APPPATH . 'controllers/' . $controller);
            });
        }
        
        echo "</div>";
    }
    
    private function testViews()
    {
        echo "<div class='test-section'>";
        echo "<h2>View Tests</h2>";
        
        // Test view files are readable
        $view_files = [
            'application/views/front-end/ritzmaude/template.php',
            'application/views/front-end/ritzmaude/header.php',
            'application/views/front-end/ritzmaude/footer.php',
            'application/views/front-end/ritzmaude/pages/home.php',
            'application/views/front-end/ritzmaude/pages/checkout.php'
        ];
        
        foreach ($view_files as $file) {
            $this->runTest("View file '$file' readable", function() use ($file) {
                return is_readable($file);
            });
        }
        
        echo "</div>";
    }
    
    private function testAPIEndpoints()
    {
        echo "<div class='test-section'>";
        echo "<h2>API Endpoint Tests</h2>";
        
        // Test if we can access API endpoints (basic check)
        $this->runTest('API Base URL Accessible', function() {
            $api_path = APPPATH . '../app/v1/';
            return is_dir($api_path) || is_dir(APPPATH . '../app/');
        });
        
        echo "</div>";
    }
    
    private function testJavaScriptFiles()
    {
        echo "<div class='test-section'>";
        echo "<h2>JavaScript Files Tests</h2>";
        
        $js_files = [
            'assets/front_end/ritzmaude/js/api.js'
        ];
        
        foreach ($js_files as $file) {
            $this->runTest("JavaScript file '$file' exists", function() use ($file) {
                return file_exists($file);
            });
        }
        
        echo "</div>";
    }
    
    private function testCSSFiles()
    {
        echo "<div class='test-section'>";
        echo "<h2>CSS Files Tests</h2>";
        
        $css_files = [
            'assets/front_end/ritzmaude/css/notifications.css'
        ];
        
        foreach ($css_files as $file) {
            $this->runTest("CSS file '$file' exists", function() use ($file) {
                return file_exists($file);
            });
        }
        
        // Test original theme assets
        $original_assets = [
            'assets/front_end/Ritz Maude Frontpage/css/assets-base.css',
            'assets/front_end/Ritz Maude Frontpage/js/assets-global.js'
        ];
        
        foreach ($original_assets as $file) {
            $this->runTest("Original asset '$file' exists", function() use ($file) {
                return file_exists($file);
            });
        }
        
        echo "</div>";
    }
    
    private function runTest($test_name, $test_function)
    {
        try {
            $result = $test_function();
            $status = $result ? 'PASS' : 'FAIL';
            $class = $result ? 'test-pass' : 'test-fail';
            
            echo "<div class='test-item'>";
            echo "<span class='$class'>[$status]</span> $test_name";
            echo "</div>";
            
            $this->test_results[] = ['name' => $test_name, 'status' => $status];
            
        } catch (Exception $e) {
            echo "<div class='test-item'>";
            echo "<span class='test-fail'>[ERROR]</span> $test_name - " . $e->getMessage();
            echo "</div>";
            
            $this->test_results[] = ['name' => $test_name, 'status' => 'ERROR'];
        }
    }
    
    private function displaySummary()
    {
        echo "<div class='test-section'>";
        echo "<h2>Test Summary</h2>";
        
        $total = count($this->test_results);
        $passed = count(array_filter($this->test_results, function($test) {
            return $test['status'] === 'PASS';
        }));
        $failed = count(array_filter($this->test_results, function($test) {
            return $test['status'] === 'FAIL';
        }));
        $errors = count(array_filter($this->test_results, function($test) {
            return $test['status'] === 'ERROR';
        }));
        
        echo "<p><strong>Total Tests:</strong> $total</p>";
        echo "<p><strong class='test-pass'>Passed:</strong> $passed</p>";
        echo "<p><strong class='test-fail'>Failed:</strong> $failed</p>";
        echo "<p><strong class='test-fail'>Errors:</strong> $errors</p>";
        
        $success_rate = $total > 0 ? round(($passed / $total) * 100, 2) : 0;
        echo "<p><strong>Success Rate:</strong> $success_rate%</p>";
        
        if ($success_rate >= 90) {
            echo "<h3 class='test-pass'>🎉 Integration Successful!</h3>";
            echo "<p>Your Ritz Maude theme is successfully integrated with the eShop backend.</p>";
        } elseif ($success_rate >= 70) {
            echo "<h3 style='color: orange;'>⚠️ Integration Mostly Complete</h3>";
            echo "<p>Most features are working, but some issues need attention.</p>";
        } else {
            echo "<h3 class='test-fail'>❌ Integration Issues Detected</h3>";
            echo "<p>Several issues need to be resolved for full functionality.</p>";
        }
        
        echo "</div>";
    }
}

// Run the tests
if (php_sapi_name() === 'cli') {
    echo "Please run this script through a web browser.\n";
} else {
    $tester = new RitzMaudeIntegrationTest();
    $tester->runAllTests();
}
?>
