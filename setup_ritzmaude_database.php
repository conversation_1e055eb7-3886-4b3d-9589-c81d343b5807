<?php
/**
 * Ritz Maude Database Setup Script
 * This script ensures all necessary tables exist and sets up sample data
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'ritzm';

try {
    // Create connection
    $conn = new mysqli($host, $username, $password, $database);
    
    // Check connection
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    
    echo "<h2>Setting up Ritz Maude Database...</h2>";
    
    // Create categories table if not exists
    $categories_sql = "CREATE TABLE IF NOT EXISTS categories (
        id int(11) NOT NULL AUTO_INCREMENT,
        name varchar(255) NOT NULL,
        slug varchar(255) NOT NULL,
        description text,
        image varchar(500),
        parent_id int(11) DEFAULT 0,
        status tinyint(1) DEFAULT 1,
        sort_order int(11) DEFAULT 0,
        created_at timestamp DEFAULT CURRENT_TIMESTAMP,
        updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        <PERSON>IMAR<PERSON> KEY (id),
        UNIQUE KEY slug (slug)
    )";
    
    if ($conn->query($categories_sql) === TRUE) {
        echo "✅ Categories table ready<br>";
    } else {
        echo "❌ Error creating categories table: " . $conn->error . "<br>";
    }
    
    // Insert sample categories for Ritz Maude
    $sample_categories = [
        ['name' => 'Men', 'slug' => 'men', 'description' => 'Men\'s clothing collection'],
        ['name' => 'Women', 'slug' => 'women', 'description' => 'Women\'s clothing collection'],
        ['name' => 'Co-ords', 'slug' => 'co-ords', 'description' => 'Coordinated sets'],
        ['name' => 'Shirts', 'slug' => 'shirts', 'description' => 'Shirt collection'],
        ['name' => 'Shorts', 'slug' => 'shorts', 'description' => 'Shorts collection'],
        ['name' => 'Dresses', 'slug' => 'dresses', 'description' => 'Dress collection'],
        ['name' => 'Jumpsuits', 'slug' => 'jumpsuits', 'description' => 'Jumpsuit collection'],
        ['name' => 'Trousers', 'slug' => 'trousers', 'description' => 'Trouser collection']
    ];
    
    foreach ($sample_categories as $category) {
        $check_sql = "SELECT id FROM categories WHERE slug = '" . $category['slug'] . "'";
        $result = $conn->query($check_sql);
        
        if ($result->num_rows == 0) {
            $insert_sql = "INSERT INTO categories (name, slug, description, status) VALUES (
                '" . $conn->real_escape_string($category['name']) . "',
                '" . $conn->real_escape_string($category['slug']) . "',
                '" . $conn->real_escape_string($category['description']) . "',
                1
            )";
            
            if ($conn->query($insert_sql) === TRUE) {
                echo "✅ Added category: " . $category['name'] . "<br>";
            } else {
                echo "❌ Error adding category " . $category['name'] . ": " . $conn->error . "<br>";
            }
        } else {
            echo "ℹ️ Category already exists: " . $category['name'] . "<br>";
        }
    }
    
    // Create products table if not exists
    $products_sql = "CREATE TABLE IF NOT EXISTS products (
        id int(11) NOT NULL AUTO_INCREMENT,
        name varchar(255) NOT NULL,
        slug varchar(255) NOT NULL,
        description text,
        short_description text,
        price decimal(10,2) NOT NULL,
        sale_price decimal(10,2) DEFAULT NULL,
        sku varchar(100),
        stock_quantity int(11) DEFAULT 0,
        manage_stock tinyint(1) DEFAULT 1,
        in_stock tinyint(1) DEFAULT 1,
        category_id int(11),
        image varchar(500),
        gallery text,
        status tinyint(1) DEFAULT 1,
        featured tinyint(1) DEFAULT 0,
        weight decimal(8,2) DEFAULT NULL,
        dimensions varchar(255),
        created_at timestamp DEFAULT CURRENT_TIMESTAMP,
        updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY slug (slug),
        KEY category_id (category_id)
    )";
    
    if ($conn->query($products_sql) === TRUE) {
        echo "✅ Products table ready<br>";
    } else {
        echo "❌ Error creating products table: " . $conn->error . "<br>";
    }
    
    // Create settings table if not exists
    $settings_sql = "CREATE TABLE IF NOT EXISTS settings (
        id int(11) NOT NULL AUTO_INCREMENT,
        variable varchar(255) NOT NULL,
        value text,
        created_at timestamp DEFAULT CURRENT_TIMESTAMP,
        updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY variable (variable)
    )";
    
    if ($conn->query($settings_sql) === TRUE) {
        echo "✅ Settings table ready<br>";
    } else {
        echo "❌ Error creating settings table: " . $conn->error . "<br>";
    }
    
    // Insert basic settings
    $basic_settings = [
        ['variable' => 'site_title', 'value' => 'RITZ MAUDE'],
        ['variable' => 'site_description', 'value' => 'Premium Fashion Brand'],
        ['variable' => 'currency', 'value' => 'NGN'],
        ['variable' => 'currency_symbol', 'value' => '₦'],
        ['variable' => 'theme', 'value' => 'ritzmaude']
    ];
    
    foreach ($basic_settings as $setting) {
        $check_sql = "SELECT id FROM settings WHERE variable = '" . $setting['variable'] . "'";
        $result = $conn->query($check_sql);
        
        if ($result->num_rows == 0) {
            $insert_sql = "INSERT INTO settings (variable, value) VALUES (
                '" . $conn->real_escape_string($setting['variable']) . "',
                '" . $conn->real_escape_string($setting['value']) . "'
            )";
            
            if ($conn->query($insert_sql) === TRUE) {
                echo "✅ Added setting: " . $setting['variable'] . "<br>";
            }
        }
    }
    
    echo "<h3>Database setup completed successfully!</h3>";
    echo "<p><a href='check_database_status.php'>Check Database Status</a> | <a href='index.php'>Go to Website</a></p>";
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<h2>Database Error:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>
