<?php
defined('BASEPATH') or exit('No direct script access allowed');

// Load categories for navigation
$this->load->model('category_model');
$categories = $this->category_model->get_categories(null, 8);
if (!is_array($categories)) {
    $categories = [];
}
?>

<!-- Header Section -->
<div class="shopify-section shopify-section-group-header-group section-header">
    
    <!-- Header Styles -->
    <style>
        /* Black and Gold Color Scheme Variables */
        :root {
            --brand-black: #000000;
            --brand-white: #ffffff;
            --brand-gold: #D4AF37;
            --brand-gold-dark: #B8941F;
            --brand-light-gray: #f5f5f5;
        }

        header-drawer {
            justify-self: start;
            margin-left: -1.2rem;
        }
        @media screen and (min-width: 990px) {
            header-drawer {
                display: none;
            }
        }
        .menu-drawer-container {
            display: flex;
        }
        .list-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .list-menu--inline {
            display: inline-flex;
            flex-wrap: wrap;
        }
        summary.list-menu__item {
            padding-right: 2.7rem;
        }
        .list-menu__item {
            display: flex;
            align-items: center;
            line-height: calc(1 + 0.3 / var(--font-body-scale));
        }
        .list-menu__item--link {
            text-decoration: none;
            padding-bottom: 1rem;
            padding-top: 1rem;
            line-height: calc(1 + 0.8 / var(--font-body-scale));
            color: var(--brand-black);
            transition: color 0.3s ease;
        }
        .list-menu__item--link:hover {
            color: var(--brand-gold);
        }
        @media screen and (min-width: 750px) {
            .list-menu__item--link {
                padding-bottom: 0.5rem;
                padding-top: 0.5rem;
            }
        }
        .header {
            padding: 10px 3rem 10px 3rem;
            background-color: var(--brand-white);
            border-bottom: 1px solid var(--brand-light-gray);
        }

        .section-header {
            position: sticky;
            margin-bottom: 0px;
        }

        @media screen and (min-width: 750px) {
            .section-header {
                margin-bottom: 0px;
            }
        }

        @media screen and (min-width: 990px) {
            .header {
                padding-top: 20px;
                padding-bottom: 20px;
            }
        }

        /* Header Icons Styling */
        .header__icon {
            color: var(--brand-black);
            transition: color 0.3s ease;
        }
        .header__icon:hover {
            color: var(--brand-gold);
        }

        /* Logo Styling */
        .header__heading-logo {
            max-height: 35px;
            width: auto;
        }

        /* Navigation Menu Styling */
        .header__menu .list-menu__item {
            color: var(--brand-black);
        }
        .header__menu .list-menu__item:hover {
            color: var(--brand-gold);
        }

        /* Mobile Menu Drawer */
        .menu-drawer {
            background-color: var(--brand-white);
        }
        .menu-drawer__menu-item {
            color: var(--brand-black);
            border-bottom: 1px solid var(--brand-light-gray);
        }
        .menu-drawer__menu-item:hover {
            color: var(--brand-gold);
            background-color: rgba(212, 175, 55, 0.1);
        }

        /* Search Modal */
        .search-modal {
            background-color: var(--brand-white);
        }
        .search__input {
            border-color: var(--brand-light-gray);
        }
        .search__input:focus {
            border-color: var(--brand-gold);
            box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
        }

        /* Cart Icon */
        .cart-count-bubble {
            background-color: var(--brand-gold);
            color: var(--brand-black);
        }

        /* Responsive adjustments */
        @media (max-width: 989px) {
            .header {
                padding: 10px 1rem;
            }
        }
        .section-header {
            position: sticky;
            margin-bottom: 0px;
        }
        @media screen and (min-width: 990px) {
            .header {
                padding-top: 20px;
                padding-bottom: 20px;
            }
        }
    </style>

    <!-- SVG Icons -->
    <svg xmlns="http://www.w3.org/2000/svg" class="hidden">
        <symbol id="icon-search" viewbox="0 0 18 19" fill="none">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M11.03 11.68A5.784 5.784 0 112.85 3.5a5.784 5.784 0 018.18 8.18zm.26 1.12a6.78 6.78 0 11.72-.7l5.4 5.4a.5.5 0 11-.71.7l-5.41-5.4z" fill="currentColor"></path>
        </symbol>
        <symbol id="icon-reset" class="icon icon-close" fill="none" viewbox="0 0 18 18" stroke="currentColor">
            <circle r="8.5" cy="9" cx="9" stroke-opacity="0.2"></circle>
            <path d="M6.82972 6.82915L1.17193 1.17097" stroke-linecap="round" stroke-linejoin="round" transform="translate(5 5)"></path>
            <path d="M1.22896 6.88502L6.77288 1.11523" stroke-linecap="round" stroke-linejoin="round" transform="translate(5 5)"></path>
        </symbol>
        <symbol id="icon-close" class="icon icon-close" fill="none" viewbox="0 0 18 17">
            <path d="M.865 15.978a.5.5 0 00.707.707l7.433-7.431 7.579 7.282a.501.501 0 00.846-.37.5.5 0 00-.153-.351L9.712 8.546l7.417-7.416a.5.5 0 10-.707-.708L8.991 7.853 1.413.573a.5.5 0 10-.693.72l7.563 7.268-7.418 7.417z" fill="currentColor"></path>
        </symbol>
        <symbol id="icon-hamburger" fill="none" viewbox="0 0 18 16">
            <path d="M1 .5a.5.5 0 100 1h15.71a.5.5 0 000-1H1zM.5 8a.5.5 0 01.5-.5h15.71a.5.5 0 010 1H1A.5.5 0 01.5 8zm0 7a.5.5 0 01.5-.5h15.71a.5.5 0 010 1H1a.5.5 0 01-.5-.5z" fill="currentColor"></path>
        </symbol>
        <symbol id="icon-cart" class="icon icon-cart" fill="none" viewbox="0 0 18 19">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.578 1.5H1.5a.5.5 0 000 1h1.578l.855 10.26A2.25 2.25 0 006.18 15h5.64a2.25 2.25 0 002.247-2.24L14.922 2.5H5.578l-.855-1H3.578zM5.922 3.5h8.156l-.855 10.26a1.25 1.25 0 01-1.248 1.24H6.18a1.25 1.25 0 01-1.248-1.24L5.922 3.5z" fill="currentColor"></path>
            <path d="M6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3zM12.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z" fill="currentColor"></path>
        </symbol>
    </svg>

    <!-- Sticky Header -->
    <sticky-header data-sticky-type="on-scroll-up" class="header-wrapper color-scheme-1 gradient header-wrapper--border-bottom">
        <header class="header header--middle-center header--mobile-center page-width header--has-menu header--has-social">

            <!-- Mobile Menu Drawer -->
            <header-drawer data-breakpoint="tablet">
                <details id="Details-menu-drawer-container" class="menu-drawer-container">
                    <summary class="header__icon header__icon--menu header__icon--summary link focus-inset" aria-label="Menu">
                        <span>
                            <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" class="icon icon-hamburger" fill="none" viewbox="0 0 18 16">
                                <use href="#icon-hamburger"></use>
                            </svg>
                        </span>
                    </summary>
                    
                    <!-- Mobile Menu Content -->
                    <div id="menu-drawer" class="gradient menu-drawer motion-reduce color-scheme-1">
                        <div class="menu-drawer__inner-container">
                            <div class="menu-drawer__navigation-container">
                                <nav class="menu-drawer__navigation">
                                    <ul class="menu-drawer__menu has-submenu list-menu" role="list">
                                        <!-- Dynamic Categories will be loaded here -->
                                        <?php if (isset($categories) && !empty($categories)): ?>
                                            <?php foreach ($categories as $category): ?>
                                                <li>
                                                    <a href="<?= base_url('products?category=' . $category['slug']) ?>" class="menu-drawer__menu-item list-menu__item link link--text focus-inset">
                                                        <?= strtoupper($category['name']) ?>
                                                    </a>
                                                </li>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                        
                                        <li>
                                            <a href="<?= base_url('products?filter=new') ?>" class="menu-drawer__menu-item list-menu__item link link--text focus-inset">
                                                NEW IN
                                            </a>
                                        </li>
                                        <li>
                                            <a href="<?= base_url('about') ?>" class="menu-drawer__menu-item list-menu__item link link--text focus-inset">
                                                ABOUT
                                            </a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </details>
            </header-drawer>

            <!-- Desktop Navigation -->
            <nav class="header__inline-menu">
                <ul class="list-menu list-menu--inline" role="list">
                    <li>
                        <header-menu>
                            <details id="Details-HeaderMenu-1" class="mega-menu">
                                <summary id="HeaderMenu-shop" class="header__menu-item list-menu__item link focus-inset">
                                    <span>SHOP</span>
                                    <svg aria-hidden="true" focusable="false" class="icon icon-caret" viewbox="0 0 10 6">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M9.354.646a.5.5 0 00-.708 0L5 4.293 1.354.646a.5.5 0 00-.708.708l4 4a.5.5 0 00.708 0l4-4a.5.5 0 000-.708z" fill="currentColor"></path>
                                    </svg>
                                </summary>
                                <div id="MegaMenu-Content-1" class="mega-menu__content color-scheme-1 gradient motion-reduce global-settings-popup" tabindex="-1">
                                    <ul class="mega-menu__list page-width" role="list">
                                        <!-- Dynamic Categories will be loaded here -->
                                        <?php if (isset($categories) && !empty($categories)): ?>
                                            <?php foreach ($categories as $category): ?>
                                                <li>
                                                    <a href="<?= base_url('products?category=' . $category['slug']) ?>" class="mega-menu__link mega-menu__link--level-2 link">
                                                        <?= strtoupper($category['name']) ?>
                                                    </a>
                                                    <?php if (isset($category['subcategories']) && !empty($category['subcategories'])): ?>
                                                        <ul class="list-unstyled" role="list">
                                                            <?php foreach ($category['subcategories'] as $subcategory): ?>
                                                                <li>
                                                                    <a href="<?= base_url('products?category=' . $subcategory['slug']) ?>" class="mega-menu__link link">
                                                                        <?= strtoupper($subcategory['name']) ?>
                                                                    </a>
                                                                </li>
                                                            <?php endforeach; ?>
                                                        </ul>
                                                    <?php endif; ?>
                                                </li>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                            </details>
                        </header-menu>
                    </li>
                    <li>
                        <a href="<?= base_url('products?filter=new') ?>" class="header__menu-item list-menu__item link link--text focus-inset">
                            <span>NEW IN</span>
                        </a>
                    </li>
                    <li>
                        <a href="<?= base_url('about') ?>" class="header__menu-item list-menu__item link link--text focus-inset">
                            <span>ABOUT</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <!-- Logo -->
            <h1 class="header__heading">
                <a href="<?= base_url() ?>" class="header__heading-link link link--text focus-inset">
                    <div class="header__heading-logo-wrapper">
                        <?php if (isset($web_settings['web_logo']) && !empty($web_settings['web_logo'])): ?>
                            <img src="<?= base_url($web_settings['web_logo']) ?>" alt="<?= isset($web_settings['site_title']) ? $web_settings['site_title'] : 'RITZ MAUDE' ?>" 
                                 width="150" height="21" loading="eager" class="header__heading-logo" sizes="(min-width: 750px) 150px, 50vw">
                        <?php else: ?>
                            <img src="<?= base_url('assets/front_end/Ritz Maude Frontpage/images/files-ANDREW-TABITHA-LOGO_ca40d13e-84d6-496e-bd32-9611534d64e3.png') ?>" 
                                 alt="RITZ MAUDE" width="150" height="21" loading="eager" class="header__heading-logo" sizes="(min-width: 750px) 150px, 50vw">
                        <?php endif; ?>
                    </div>
                </a>
            </h1>

            <!-- Header Icons -->
            <div class="header__icons header__icons--localization header-localization">
                <!-- Search Icon -->
                <details-modal class="header__search">
                    <details>
                        <summary class="header__icon header__icon--search header__icon--summary link focus-inset modal__toggle" aria-haspopup="dialog" aria-label="Search">
                            <span>
                                <svg class="modal__toggle-open icon icon-search" aria-hidden="true" focusable="false">
                                    <use href="#icon-search"></use>
                                </svg>
                                <svg class="modal__toggle-close icon icon-close" aria-hidden="true" focusable="false">
                                    <use href="#icon-close"></use>
                                </svg>
                            </span>
                        </summary>
                        <div class="search-modal modal__content gradient" role="dialog" aria-modal="true" aria-label="Search">
                            <div class="modal-overlay"></div>
                            <div class="search-modal__content search-modal__content-bottom" tabindex="-1">
                                <search-form class="search search-modal__form">
                                    <form action="<?= base_url('products/search') ?>" method="get" role="search" class="search__form">
                                        <div class="field">
                                            <input class="search__input field__input" id="Search-In-Modal" type="search" name="q" value="" placeholder="Search" role="combobox" aria-expanded="false" aria-owns="predictive-search-results" aria-controls="predictive-search-results" aria-describedby="predictive-search-live-region" aria-autocomplete="list" autocorrect="off" autocomplete="off" autocapitalize="off" spellcheck="false">
                                            <label class="field__label" for="Search-In-Modal">Search</label>
                                            <input type="hidden" name="options[prefix]" value="last">
                                            <button type="submit" class="search__button field__button" aria-label="Search">
                                                <svg class="icon icon-search" aria-hidden="true" focusable="false">
                                                    <use href="#icon-search"></use>
                                                </svg>
                                            </button>
                                        </div>
                                    </form>
                                </search-form>
                            </div>
                        </div>
                    </details>
                </details-modal>

                <!-- Account Icon -->
                <a href="<?= base_url($is_logged_in ? 'my-account' : 'login') ?>" class="header__icon header__icon--account link focus-inset">
                    <svg class="icon icon-account" aria-hidden="true" focusable="false" xmlns="http://www.w3.org/2000/svg" fill="none" viewbox="0 0 18 19">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M6 4.5a3 3 0 116 0 3 3 0 01-6 0zm3-4a4 4 0 100 8 4 4 0 000-8zm5.58 12.15c1.12.82 1.83 2.24 1.91 4.85H1.51c.08-2.6.79-4.03 1.9-4.85C4.66 11.75 6.5 11.5 9 11.5s4.35.26 5.58 1.15zM9 10.5c-2.5 0-4.65.24-6.17 1.35C1.27 12.98.5 14.93.5 18v.5h17V18c0-3.07-.77-5.02-2.33-6.15-1.52-1.1-3.67-1.35-6.17-1.35z" fill="currentColor"></path>
                    </svg>
                    <span class="visually-hidden"><?= $is_logged_in ? 'Account' : 'Log in' ?></span>
                </a>

                <!-- Cart Icon -->
                <a href="<?= base_url('cart') ?>" class="header__icon header__icon--cart link focus-inset" id="cart-icon-bubble" style="width: 44px; height: 44px;">
                    <svg class="icon icon-cart" aria-hidden="true" focusable="false" xmlns="http://www.w3.org/2000/svg" viewbox="0 0 18 19" fill="none" style="width: 20px; height: 20px;">
                        <use href="#icon-cart"></use>
                    </svg>
                    <span class="visually-hidden">Cart</span>
                    <div class="cart-count-bubble" style="background-color: #8B1538; color: #fff; font-size: 0.75rem; width: 16px; height: 16px; bottom: 0.5rem; left: 1.8rem;">
                        <span aria-hidden="true" class="cart-count">0</span>
                        <span class="visually-hidden">items</span>
                    </div>
                </a>
            </div>
        </header>
    </sticky-header>
</div>

<!-- Load categories for navigation -->
<script>
$(document).ready(function() {
    // Load categories for navigation if not already loaded
    if (!window.categoriesLoaded) {
        $.ajax({
            url: '<?= base_url('app/v1/api/get_categories') ?>',
            type: 'POST',
            data: {
                [window.eShop.csrfName]: window.eShop.csrfHash
            },
            success: function(response) {
                if (response.error === false && response.data) {
                    // Categories loaded successfully
                    window.categoriesLoaded = true;
                }
            }
        });
    }
});
</script>
