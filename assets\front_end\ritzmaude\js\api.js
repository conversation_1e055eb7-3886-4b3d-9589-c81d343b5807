/**
 * Ritz Maude Theme API Wrapper
 * Handles all API interactions for the frontend
 */

// Global eShop object for API management
window.eShop = window.eShop || {};

// API Configuration
window.eShop.config = {
    baseUrl: window.location.origin + window.location.pathname.split('/').slice(0, -1).join('/') + '/',
    apiUrl: window.location.origin + window.location.pathname.split('/').slice(0, -1).join('/') + '/app/v1/api/',
    csrfName: 'csrf_test_name',
    csrfHash: '',
    currencySymbol: '₦',
    isLoggedIn: false,
    userId: null,
    userToken: null
};

// Initialize API configuration
window.eShop.init = function() {
    // Get CSRF token from meta tags or forms
    var csrfToken = $('meta[name="csrf-token"]').attr('content') || 
                   $('input[name="csrf_test_name"]').val() || 
                   '';
    
    if (csrfToken) {
        window.eShop.config.csrfHash = csrfToken;
    }
    
    // Check if user is logged in
    window.eShop.checkAuthStatus();
    
    // Initialize cart
    window.eShop.cart.init();
};

// Authentication methods
window.eShop.auth = {
    login: function(credentials, callback) {
        $.ajax({
            url: window.eShop.config.apiUrl + 'login',
            type: 'POST',
            data: $.extend({
                [window.eShop.config.csrfName]: window.eShop.config.csrfHash
            }, credentials),
            dataType: 'json',
            success: function(response) {
                if (response.error === false) {
                    window.eShop.config.isLoggedIn = true;
                    window.eShop.config.userId = response.data.id;
                    window.eShop.config.userToken = response.data.apikey;
                    
                    // Store in localStorage
                    localStorage.setItem('eShop_user_id', response.data.id);
                    localStorage.setItem('eShop_user_token', response.data.apikey);
                    localStorage.setItem('eShop_user_data', JSON.stringify(response.data));
                }
                if (callback) callback(response);
            },
            error: function(xhr, status, error) {
                if (callback) callback({error: true, message: 'Login failed. Please try again.'});
            }
        });
    },
    
    register: function(userData, callback) {
        $.ajax({
            url: window.eShop.config.apiUrl + 'register_user',
            type: 'POST',
            data: $.extend({
                [window.eShop.config.csrfName]: window.eShop.config.csrfHash
            }, userData),
            dataType: 'json',
            success: function(response) {
                if (callback) callback(response);
            },
            error: function(xhr, status, error) {
                if (callback) callback({error: true, message: 'Registration failed. Please try again.'});
            }
        });
    },
    
    logout: function(callback) {
        // Clear local storage
        localStorage.removeItem('eShop_user_id');
        localStorage.removeItem('eShop_user_token');
        localStorage.removeItem('eShop_user_data');
        
        window.eShop.config.isLoggedIn = false;
        window.eShop.config.userId = null;
        window.eShop.config.userToken = null;
        
        // Redirect to login page or home
        window.location.href = window.eShop.config.baseUrl;
    },
    
    forgotPassword: function(email, callback) {
        $.ajax({
            url: window.eShop.config.apiUrl + 'reset_password',
            type: 'POST',
            data: {
                [window.eShop.config.csrfName]: window.eShop.config.csrfHash,
                email: email
            },
            dataType: 'json',
            success: function(response) {
                if (callback) callback(response);
            },
            error: function(xhr, status, error) {
                if (callback) callback({error: true, message: 'Failed to send reset email.'});
            }
        });
    }
};

// Check authentication status
window.eShop.checkAuthStatus = function() {
    var userId = localStorage.getItem('eShop_user_id');
    var userToken = localStorage.getItem('eShop_user_token');
    
    if (userId && userToken) {
        window.eShop.config.isLoggedIn = true;
        window.eShop.config.userId = userId;
        window.eShop.config.userToken = userToken;
    }
};

// Product API methods
window.eShop.products = {
    getAll: function(params, callback) {
        var defaultParams = {
            limit: 12,
            offset: 0,
            sort: 'p.date_added',
            order: 'DESC'
        };
        
        var requestData = $.extend({
            [window.eShop.config.csrfName]: window.eShop.config.csrfHash
        }, defaultParams, params);
        
        $.ajax({
            url: window.eShop.config.apiUrl + 'get_products',
            type: 'POST',
            data: requestData,
            dataType: 'json',
            success: function(response) {
                if (callback) callback(response);
            },
            error: function(xhr, status, error) {
                if (callback) callback({error: true, message: 'Failed to load products'});
            }
        });
    },
    
    getById: function(productId, callback) {
        $.ajax({
            url: window.eShop.config.apiUrl + 'get_products',
            type: 'POST',
            data: {
                [window.eShop.config.csrfName]: window.eShop.config.csrfHash,
                id: productId
            },
            dataType: 'json',
            success: function(response) {
                if (callback) callback(response);
            },
            error: function(xhr, status, error) {
                if (callback) callback({error: true, message: 'Failed to load product details'});
            }
        });
    },
    
    search: function(query, params, callback) {
        var requestData = $.extend({
            [window.eShop.config.csrfName]: window.eShop.config.csrfHash,
            search: query
        }, params);
        
        $.ajax({
            url: window.eShop.config.apiUrl + 'get_products',
            type: 'POST',
            data: requestData,
            dataType: 'json',
            success: function(response) {
                if (callback) callback(response);
            },
            error: function(xhr, status, error) {
                if (callback) callback({error: true, message: 'Search failed'});
            }
        });
    }
};

// Categories API methods
window.eShop.categories = {
    getAll: function(callback) {
        $.ajax({
            url: window.eShop.config.apiUrl + 'get_categories',
            type: 'POST',
            data: {
                [window.eShop.config.csrfName]: window.eShop.config.csrfHash
            },
            dataType: 'json',
            success: function(response) {
                if (callback) callback(response);
            },
            error: function(xhr, status, error) {
                if (callback) callback({error: true, message: 'Failed to load categories'});
            }
        });
    }
};

// Cart management
window.eShop.cart = {
    items: [],
    count: 0,
    
    init: function() {
        this.updateCartDisplay();
    },
    
    addToCart: function(productId, variantId, quantity, callback) {
        return this.add(productId, variantId, quantity, callback);
    },

    removeFromCart: function(cartId, callback) {
        return this.remove(cartId, callback);
    },

    add: function(productId, variantId, quantity, callback) {
        if (!window.eShop.config.isLoggedIn) {
            // Redirect to login
            window.location.href = window.eShop.config.baseUrl + 'customer-auth/login?redirect=' + encodeURIComponent(window.location.pathname);
            return;
        }

        $.ajax({
            url: window.eShop.config.baseUrl + 'cart/manage',
            type: 'POST',
            data: {
                [window.eShop.config.csrfName]: window.eShop.config.csrfHash,
                user_id: window.eShop.config.userId,
                product_variant_id: variantId,
                qty: quantity
            },
            dataType: 'json',
            success: function(response) {
                if (response.error === false) {
                    window.eShop.cart.updateCartDisplay();
                    window.eShop.cart.showNotification('Item added to cart!', 'success');
                } else {
                    window.eShop.cart.showNotification(response.message || 'Failed to add item to cart', 'error');
                }
                if (callback) callback(response);
            },
            error: function(xhr, status, error) {
                window.eShop.cart.showNotification('Failed to add item to cart', 'error');
                if (callback) callback({error: true, message: 'Failed to add item to cart'});
            }
        });
    },
    
    get: function(callback) {
        if (!window.eShop.config.isLoggedIn) {
            if (callback) callback({error: true, message: 'User not logged in'});
            return;
        }

        $.ajax({
            url: window.eShop.config.apiUrl + 'get_user_cart',
            type: 'POST',
            data: {
                [window.eShop.config.csrfName]: window.eShop.config.csrfHash,
                user_id: window.eShop.config.userId
            },
            dataType: 'json',
            success: function(response) {
                if (response.error === false && response.data) {
                    window.eShop.cart.items = response.data;
                    window.eShop.cart.count = response.data.length;
                }
                if (callback) callback(response);
            },
            error: function(xhr, status, error) {
                if (callback) callback({error: true, message: 'Failed to load cart'});
            }
        });
    },
    
    update: function(variantId, quantity, callback) {
        $.ajax({
            url: window.eShop.config.baseUrl + 'cart/manage',
            type: 'POST',
            data: {
                [window.eShop.config.csrfName]: window.eShop.config.csrfHash,
                user_id: window.eShop.config.userId,
                product_variant_id: variantId,
                qty: quantity
            },
            dataType: 'json',
            success: function(response) {
                if (response.error === false) {
                    window.eShop.cart.updateCartDisplay();
                }
                if (callback) callback(response);
            },
            error: function(xhr, status, error) {
                if (callback) callback({error: true, message: 'Failed to update cart'});
            }
        });
    },
    
    remove: function(variantId, callback) {
        $.ajax({
            url: window.eShop.config.baseUrl + 'cart/remove',
            type: 'POST',
            data: {
                [window.eShop.config.csrfName]: window.eShop.config.csrfHash,
                product_variant_id: variantId
            },
            dataType: 'json',
            success: function(response) {
                if (response.error === false) {
                    window.eShop.cart.updateCartDisplay();
                    window.eShop.cart.showNotification('Item removed from cart', 'success');
                }
                if (callback) callback(response);
            },
            error: function(xhr, status, error) {
                if (callback) callback({error: true, message: 'Failed to remove item'});
            }
        });
    },
    
    updateCartDisplay: function() {
        this.get(function(response) {
            if (response.error === false && response.data) {
                var count = response.data.length;
                $('.cart-count').text(count);
                $('.cart-count-bubble').text(count).toggle(count > 0);
                
                // Update cart icon in header
                if (count > 0) {
                    $('.cart-icon').addClass('has-items');
                } else {
                    $('.cart-icon').removeClass('has-items');
                }
            }
        });
    },
    
    showNotification: function(message, type) {
        // Create notification element
        var notification = $('<div class="cart-notification cart-notification--' + type + '">' + message + '</div>');
        
        // Add to page
        $('body').append(notification);
        
        // Show notification
        setTimeout(function() {
            notification.addClass('show');
        }, 100);
        
        // Hide and remove notification
        setTimeout(function() {
            notification.removeClass('show');
            setTimeout(function() {
                notification.remove();
            }, 300);
        }, 3000);
    }
};

// Settings and configuration
window.eShop.settings = {
    get: function(callback) {
        $.ajax({
            url: window.eShop.config.apiUrl + 'get_settings',
            type: 'POST',
            data: {
                [window.eShop.config.csrfName]: window.eShop.config.csrfHash
            },
            dataType: 'json',
            success: function(response) {
                if (response.error === false && response.data) {
                    // Update currency symbol if available
                    if (response.data.currency_symbol) {
                        window.eShop.config.currencySymbol = response.data.currency_symbol;
                    }
                }
                if (callback) callback(response);
            },
            error: function(xhr, status, error) {
                if (callback) callback({error: true, message: 'Failed to load settings'});
            }
        });
    }
};

// Utility functions
window.eShop.utils = {
    formatPrice: function(price) {
        return window.eShop.config.currencySymbol + parseFloat(price).toLocaleString();
    },
    
    formatDate: function(dateString) {
        return new Date(dateString).toLocaleDateString();
    },
    
    debounce: function(func, wait, immediate) {
        var timeout;
        return function() {
            var context = this, args = arguments;
            var later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            var callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }
};

// Initialize when document is ready
$(document).ready(function() {
    window.eShop.init();
});

// Export for use in other scripts
window.eShop.baseUrl = window.eShop.config.baseUrl;
window.eShop.apiUrl = window.eShop.config.apiUrl;
window.eShop.csrfName = window.eShop.config.csrfName;
window.eShop.csrfHash = window.eShop.config.csrfHash;
window.eShop.currencySymbol = window.eShop.config.currencySymbol;
window.eShop.isLoggedIn = window.eShop.config.isLoggedIn;
