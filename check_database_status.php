<?php
/**
 * Quick database status check for Ritz Maude
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'ritzm';

try {
    // Create connection
    $conn = new mysqli($host, $username, $password, $database);
    
    // Check connection
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    
    echo "<h2>Database Connection: SUCCESS</h2>";
    echo "<p>Connected to database: <strong>$database</strong></p>";
    
    // Check if tables exist
    $tables_to_check = [
        'categories',
        'products', 
        'users',
        'settings',
        'themes',
        'cart',
        'orders'
    ];
    
    echo "<h3>Table Status:</h3>";
    echo "<ul>";
    
    foreach ($tables_to_check as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            // Get row count
            $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
            $count = $count_result ? $count_result->fetch_assoc()['count'] : 0;
            echo "<li><strong>$table</strong>: ✅ EXISTS ($count rows)</li>";
        } else {
            echo "<li><strong>$table</strong>: ❌ NOT FOUND</li>";
        }
    }
    echo "</ul>";
    
    // Check categories specifically
    $categories_result = $conn->query("SELECT * FROM categories LIMIT 10");
    if ($categories_result && $categories_result->num_rows > 0) {
        echo "<h3>Sample Categories:</h3>";
        echo "<ul>";
        while ($row = $categories_result->fetch_assoc()) {
            echo "<li>" . htmlspecialchars($row['name']) . " (ID: " . $row['id'] . ")</li>";
        }
        echo "</ul>";
    }
    
    // Check products
    $products_result = $conn->query("SELECT * FROM products LIMIT 5");
    if ($products_result && $products_result->num_rows > 0) {
        echo "<h3>Sample Products:</h3>";
        echo "<ul>";
        while ($row = $products_result->fetch_assoc()) {
            echo "<li>" . htmlspecialchars($row['name']) . " - " . htmlspecialchars($row['price']) . "</li>";
        }
        echo "</ul>";
    }
    
    // Check current theme
    $theme_result = $conn->query("SELECT * FROM themes WHERE is_default = 1");
    if ($theme_result && $theme_result->num_rows > 0) {
        $theme = $theme_result->fetch_assoc();
        echo "<h3>Current Theme:</h3>";
        echo "<p><strong>" . htmlspecialchars($theme['name']) . "</strong> (Slug: " . htmlspecialchars($theme['slug']) . ")</p>";
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<h2>Database Error:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>
