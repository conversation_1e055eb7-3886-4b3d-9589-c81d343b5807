/**
 * Ritz Maude Theme Performance Optimization
 * Handles lazy loading, caching, and performance improvements
 */

// Performance optimization object
window.RitzMaudePerformance = {
    // Cache for API responses
    cache: new Map(),
    
    // Debounce utility
    debounce: function(func, wait, immediate) {
        var timeout;
        return function() {
            var context = this, args = arguments;
            var later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            var callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    },
    
    // Throttle utility
    throttle: function(func, limit) {
        var inThrottle;
        return function() {
            var args = arguments;
            var context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(function() { inThrottle = false; }, limit);
            }
        };
    },
    
    // Lazy load images
    initLazyLoading: function() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        img.classList.add('loaded');
                        observer.unobserve(img);
                    }
                });
            });
            
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        } else {
            // Fallback for older browsers
            document.querySelectorAll('img[data-src]').forEach(img => {
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                img.classList.add('loaded');
            });
        }
    },
    
    // Cache API responses
    cacheResponse: function(key, data, ttl = 300000) { // 5 minutes default TTL
        const item = {
            data: data,
            timestamp: Date.now(),
            ttl: ttl
        };
        this.cache.set(key, item);
    },
    
    // Get cached response
    getCachedResponse: function(key) {
        const item = this.cache.get(key);
        if (!item) return null;
        
        if (Date.now() - item.timestamp > item.ttl) {
            this.cache.delete(key);
            return null;
        }
        
        return item.data;
    },
    
    // Enhanced AJAX with caching
    cachedAjax: function(options) {
        const cacheKey = options.url + JSON.stringify(options.data || {});
        const cached = this.getCachedResponse(cacheKey);
        
        if (cached && options.cache !== false) {
            if (options.success) {
                setTimeout(() => options.success(cached), 0);
            }
            return;
        }
        
        const originalSuccess = options.success;
        options.success = (response) => {
            if (options.cache !== false) {
                this.cacheResponse(cacheKey, response, options.cacheTTL);
            }
            if (originalSuccess) {
                originalSuccess(response);
            }
        };
        
        $.ajax(options);
    },
    
    // Preload critical resources
    preloadResources: function() {
        const criticalResources = [
            '/app/v1/api/get_categories',
            '/app/v1/api/get_settings'
        ];
        
        criticalResources.forEach(url => {
            if (window.eShop && window.eShop.config) {
                this.cachedAjax({
                    url: window.eShop.config.baseUrl + url.substring(1),
                    type: 'POST',
                    data: {
                        [window.eShop.config.csrfName]: window.eShop.config.csrfHash
                    },
                    cache: true,
                    cacheTTL: 600000 // 10 minutes
                });
            }
        });
    },
    
    // Optimize images
    optimizeImages: function() {
        document.querySelectorAll('img').forEach(img => {
            // Add loading="lazy" for modern browsers
            if (!img.hasAttribute('loading')) {
                img.setAttribute('loading', 'lazy');
            }
            
            // Add error handling
            img.addEventListener('error', function() {
                if (!this.hasAttribute('data-error-handled')) {
                    this.src = window.eShop?.config?.baseUrl + 'assets/no-image.png' || '/assets/no-image.png';
                    this.setAttribute('data-error-handled', 'true');
                }
            });
        });
    },
    
    // Minimize DOM queries
    cacheSelectors: function() {
        window.RitzMaudeSelectors = {
            $window: $(window),
            $document: $(document),
            $body: $('body'),
            $header: $('header'),
            $footer: $('footer'),
            $cartCount: $('.cart-count'),
            $cartBubble: $('.cart-count-bubble')
        };
    },
    
    // Optimize scroll events
    initScrollOptimization: function() {
        let ticking = false;
        
        const updateScrollElements = () => {
            const scrollTop = window.pageYOffset;
            
            // Update scroll-dependent elements
            document.querySelectorAll('[data-scroll]').forEach(element => {
                const scrollValue = element.getAttribute('data-scroll');
                if (scrollValue === 'fade') {
                    const opacity = Math.max(0, 1 - scrollTop / 500);
                    element.style.opacity = opacity;
                }
            });
            
            ticking = false;
        };
        
        const requestScrollUpdate = () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollElements);
                ticking = true;
            }
        };
        
        window.addEventListener('scroll', this.throttle(requestScrollUpdate, 16));
    },
    
    // Optimize form submissions
    optimizeForms: function() {
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const submitBtn = this.querySelector('button[type="submit"], input[type="submit"]');
                if (submitBtn && !submitBtn.disabled) {
                    submitBtn.disabled = true;
                    const originalText = submitBtn.textContent || submitBtn.value;
                    
                    if (submitBtn.tagName === 'BUTTON') {
                        submitBtn.textContent = 'Processing...';
                    } else {
                        submitBtn.value = 'Processing...';
                    }
                    
                    // Re-enable after 5 seconds as fallback
                    setTimeout(() => {
                        submitBtn.disabled = false;
                        if (submitBtn.tagName === 'BUTTON') {
                            submitBtn.textContent = originalText;
                        } else {
                            submitBtn.value = originalText;
                        }
                    }, 5000);
                }
            });
        });
    },
    
    // Initialize all optimizations
    init: function() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
            return;
        }
        
        this.cacheSelectors();
        this.initLazyLoading();
        this.optimizeImages();
        this.initScrollOptimization();
        this.optimizeForms();
        
        // Preload resources after a short delay
        setTimeout(() => {
            this.preloadResources();
        }, 1000);
        
        // Clean cache periodically
        setInterval(() => {
            this.cleanCache();
        }, 300000); // Every 5 minutes
    },
    
    // Clean expired cache entries
    cleanCache: function() {
        const now = Date.now();
        for (const [key, item] of this.cache.entries()) {
            if (now - item.timestamp > item.ttl) {
                this.cache.delete(key);
            }
        }
    },
    
    // Monitor performance
    monitorPerformance: function() {
        if ('performance' in window) {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    const loadTime = perfData.loadEventEnd - perfData.loadEventStart;
                    
                    console.log('Ritz Maude Performance Metrics:');
                    console.log('Page Load Time:', loadTime + 'ms');
                    console.log('DOM Content Loaded:', perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart + 'ms');
                    
                    // Send to analytics if available
                    if (typeof gtag !== 'undefined') {
                        gtag('event', 'page_load_time', {
                            event_category: 'performance',
                            value: Math.round(loadTime)
                        });
                    }
                }, 0);
            });
        }
    }
};

// Auto-initialize when script loads
window.RitzMaudePerformance.init();
window.RitzMaudePerformance.monitorPerformance();

// Expose utilities globally
window.debounce = window.RitzMaudePerformance.debounce;
window.throttle = window.RitzMaudePerformance.throttle;

// Override jQuery AJAX to use caching
if (typeof $ !== 'undefined') {
    const originalAjax = $.ajax;
    $.cachedAjax = function(options) {
        return window.RitzMaudePerformance.cachedAjax(options);
    };
}
