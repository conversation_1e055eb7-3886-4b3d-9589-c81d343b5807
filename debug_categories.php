<?php
/**
 * Debug Categories Loading
 * Check if categories are loading properly
 */

// Include CodeIgniter
require_once 'index.php';

echo "<h2>🔍 Categories Debug Information</h2>";
echo "<style>
    body { font-family: 'Century Gothic', sans-serif; margin: 20px; }
    .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { color: #28a745; }
    .error { color: #dc3545; }
    .info { color: #17a2b8; }
</style>";

// Database connection
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'ritzm';

try {
    $conn = new mysqli($host, $username, $password, $database);
    
    if ($conn->connect_error) {
        echo "<div class='debug-section error'>❌ Database connection failed: " . $conn->connect_error . "</div>";
        exit;
    }
    
    echo "<div class='debug-section success'>✅ Database connected successfully</div>";
    
    // Check categories table
    echo "<div class='debug-section'>";
    echo "<h3>📂 Categories in Database</h3>";
    
    $result = $conn->query("SELECT id, name, slug, parent_id, status FROM categories ORDER BY parent_id, name");
    
    if ($result && $result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'><th>ID</th><th>Name</th><th>Slug</th><th>Parent ID</th><th>Status</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            $status_color = $row['status'] == 1 ? 'success' : 'error';
            $status_text = $row['status'] == 1 ? 'Active' : 'Inactive';
            
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['name']}</td>";
            echo "<td>{$row['slug']}</td>";
            echo "<td>{$row['parent_id']}</td>";
            echo "<td class='$status_color'>$status_text</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='error'>❌ No categories found in database</div>";
    }
    echo "</div>";
    
    // Test category model loading
    echo "<div class='debug-section'>";
    echo "<h3>🔧 Category Model Test</h3>";
    
    // Simulate CodeIgniter category loading
    $categories_query = "SELECT * FROM categories WHERE parent_id = 0 AND status = 1 ORDER BY row_order ASC LIMIT 8";
    $categories_result = $conn->query($categories_query);
    
    if ($categories_result && $categories_result->num_rows > 0) {
        echo "<div class='success'>✅ Categories loaded successfully via direct query</div>";
        echo "<div class='info'>📊 Found {$categories_result->num_rows} main categories</div>";
        
        echo "<ul>";
        while ($category = $categories_result->fetch_assoc()) {
            echo "<li><strong>{$category['name']}</strong> (/{$category['slug']})</li>";
        }
        echo "</ul>";
    } else {
        echo "<div class='error'>❌ No main categories found</div>";
    }
    echo "</div>";
    
    // Check for subcategories
    echo "<div class='debug-section'>";
    echo "<h3>📁 Subcategories Test</h3>";
    
    $subcategories_query = "SELECT c.name as category_name, sc.name as subcategory_name, sc.slug 
                           FROM categories c 
                           JOIN categories sc ON c.id = sc.parent_id 
                           WHERE c.parent_id = 0 AND c.status = 1 AND sc.status = 1";
    $subcategories_result = $conn->query($subcategories_query);
    
    if ($subcategories_result && $subcategories_result->num_rows > 0) {
        echo "<div class='success'>✅ Subcategories found</div>";
        echo "<ul>";
        while ($subcat = $subcategories_result->fetch_assoc()) {
            echo "<li>{$subcat['category_name']} → {$subcat['subcategory_name']} (/{$subcat['slug']})</li>";
        }
        echo "</ul>";
    } else {
        echo "<div class='info'>ℹ️ No subcategories found (this is normal for a simple setup)</div>";
    }
    echo "</div>";
    
    // Test array structure
    echo "<div class='debug-section'>";
    echo "<h3>🔍 Array Structure Test</h3>";
    
    $test_categories = [];
    $categories_result = $conn->query("SELECT id, name, slug FROM categories WHERE parent_id = 0 AND status = 1 LIMIT 5");
    
    if ($categories_result) {
        while ($row = $categories_result->fetch_assoc()) {
            $test_categories[] = $row;
        }
    }
    
    echo "<div class='info'>📊 Test array structure:</div>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
    print_r($test_categories);
    echo "</pre>";
    
    if (is_array($test_categories) && !empty($test_categories)) {
        echo "<div class='success'>✅ Array structure is correct</div>";
        echo "<div class='info'>🔧 This is the format expected by the header.php foreach loop</div>";
    } else {
        echo "<div class='error'>❌ Array structure issue detected</div>";
    }
    echo "</div>";
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<div class='debug-section error'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<div class='debug-section'>";
echo "<h3>🚀 Quick Links</h3>";
echo "<ul>";
echo "<li><a href='http://localhost/ritz' target='_blank'>🏠 Homepage</a></li>";
echo "<li><a href='http://localhost/ritz/products' target='_blank'>🛍️ Products Page</a></li>";
echo "<li><a href='http://localhost/ritz/cart' target='_blank'>🛒 Cart Page</a></li>";
echo "<li><a href='test_website_functionality.php' target='_blank'>🧪 Full Website Test</a></li>";
echo "</ul>";
echo "</div>";
?>
