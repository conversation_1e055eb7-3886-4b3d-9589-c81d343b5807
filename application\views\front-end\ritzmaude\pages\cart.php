<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<!-- Shopping Cart Page Content -->

<!-- <PERSON> Header -->
<section class="shopify-section section">
    <style>
        .page-header {
            padding-top: 42px;
            padding-bottom: 33px;
            text-align: center;
        }
        @media screen and (min-width: 750px) {
            .page-header {
                padding-top: 56px;
                padding-bottom: 44px;
            }
        }
    </style>
    <div class="page-header color-scheme-1 gradient">
        <div class="page-width">
            <h1 class="main-page-title page-title h0 scroll-trigger animate--fade-in">
                Your Cart
            </h1>
        </div>
    </div>
</section>

<!-- Cart Content -->
<section class="shopify-section section">
    <style>
        .section-cart-padding {
            padding-top: 27px;
            padding-bottom: 27px;
        }
        @media screen and (min-width: 750px) {
            .section-cart-padding {
                padding-top: 36px;
                padding-bottom: 36px;
            }
        }
        .cart-item {
            border-bottom: 1px solid rgba(var(--color-foreground), 0.08);
            padding: 2rem 0;
        }
        .cart-item:last-child {
            border-bottom: none;
        }
        .cart-item__media {
            width: 100px;
            height: 100px;
            object-fit: cover;
        }
        .cart-item__details {
            flex: 1;
            padding-left: 1.5rem;
        }
        .cart-item__name {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        .cart-item__price {
            font-size: 1.1rem;
            font-weight: 500;
        }
        .cart-summary {
            background: rgba(var(--color-foreground), 0.02);
            padding: 2rem;
            border-radius: 8px;
        }
        .quantity-selector {
            display: flex;
            align-items: center;
            border: 1px solid rgba(var(--color-foreground), 0.2);
            border-radius: 4px;
            width: fit-content;
        }
        .quantity-selector button {
            background: none;
            border: none;
            padding: 0.5rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .quantity-selector input {
            border: none;
            text-align: center;
            width: 60px;
            padding: 0.5rem 0;
        }
    </style>
    
    <div class="color-scheme-1 gradient">
        <div class="page-width isolate section-cart-padding">
            
            <!-- Empty Cart State -->
            <div id="empty-cart" class="cart-empty" style="display: none; text-align: center; padding: 4rem 0;">
                <h2>Your cart is empty</h2>
                <p>Looks like you haven't added anything to your cart yet.</p>
                <a href="<?= base_url('products') ?>" class="btn btn--secondary">Continue Shopping</a>
            </div>

            <!-- Cart Items -->
            <div id="cart-content" class="cart grid grid--1-col grid--2-col-tablet" style="display: none;">
                
                <!-- Cart Items List -->
                <div class="grid__item">
                    <div class="cart__items" id="cart-items">
                        <!-- Cart items will be loaded dynamically -->
                    </div>
                </div>

                <!-- Cart Summary -->
                <div class="grid__item">
                    <div class="cart-summary">
                        <h3 class="cart-summary__title">Order Summary</h3>
                        
                        <div class="cart-summary__line">
                            <span>Subtotal (<span id="cart-item-count">0</span> items)</span>
                            <span id="cart-subtotal">₦0.00</span>
                        </div>
                        
                        <div class="cart-summary__line">
                            <span>Shipping</span>
                            <span id="cart-shipping">Calculated at checkout</span>
                        </div>
                        
                        <div class="cart-summary__line cart-summary__total">
                            <strong>
                                <span>Total</span>
                                <span id="cart-total">₦0.00</span>
                            </strong>
                        </div>
                        
                        <div class="cart-summary__buttons">
                            <button type="button" class="btn btn--full-width btn--secondary" id="checkout-btn">
                                Proceed to Checkout
                            </button>
                            <a href="<?= base_url('products') ?>" class="btn btn--full-width btn--tertiary">
                                Continue Shopping
                            </a>
                        </div>
                        
                        <!-- Promo Code -->
                        <div class="cart-summary__promo">
                            <details class="cart-summary__discount-wrapper">
                                <summary class="cart-summary__discount-title">
                                    <span>Add promo code</span>
                                    <svg aria-hidden="true" focusable="false" class="icon icon-caret" viewbox="0 0 10 6">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M9.354.646a.5.5 0 00-.708 0L5 4.293 1.354.646a.5.5 0 00-.708.708l4 4a.5.5 0 00.708 0l4-4a.5.5 0 000-.708z" fill="currentColor"></path>
                                    </svg>
                                </summary>
                                <div class="cart-summary__discount-content">
                                    <form class="cart-summary__discount-form" id="promo-form">
                                        <div class="field">
                                            <input type="text" name="promo_code" id="promo-code" class="field__input" placeholder="Enter promo code">
                                            <button type="submit" class="btn btn--secondary">Apply</button>
                                        </div>
                                    </form>
                                </div>
                            </details>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loading State -->
            <div id="cart-loading" class="loading-container" style="text-align: center; padding: 4rem 0;">
                <div class="loading-spinner"></div>
                <p>Loading your cart...</p>
            </div>
        </div>
    </div>
</section>

<!-- Recommended Products -->
<section class="shopify-section section" id="recommended-section" style="display: none;">
    <div class="color-scheme-1 gradient">
        <div class="page-width isolate section-cart-padding">
            <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin">
                <h2 class="title inline-richtext h1 scroll-trigger animate--slide-in">
                    You might also like
                </h2>
            </div>
            
            <div id="recommended-products-grid" class="grid product-grid grid--2-col-tablet-down grid--4-col-desktop">
                <!-- Recommended products will be loaded dynamically -->
            </div>
        </div>
    </div>
</section>

<!-- JavaScript for Cart Functionality -->
<script>
// Global cart data
window.cartData = {
    items: [],
    subtotal: 0,
    total: 0,
    itemCount: 0
};

function initializeCart() {
    loadCartItems();
    setupCartEventListeners();
}

function loadCartItems() {
    $('#cart-loading').show();
    $('#cart-content').hide();
    $('#empty-cart').hide();
    
    $.ajax({
        url: window.eShop.apiUrl + 'get_user_cart',
        type: 'POST',
        data: {
            [window.eShop.csrfName]: window.eShop.csrfHash
        },
        success: function(response) {
            $('#cart-loading').hide();
            
            if (response.error === false && response.data && response.data.length > 0) {
                window.cartData.items = response.data;
                renderCartItems(response.data);
                calculateCartTotals();
                $('#cart-content').show();
                loadRecommendedProducts();
            } else {
                $('#empty-cart').show();
            }
        },
        error: function() {
            $('#cart-loading').hide();
            $('#empty-cart').show();
        }
    });
}

function renderCartItems(items) {
    var html = '';
    
    items.forEach(function(item) {
        var imageUrl = item.image ? window.eShop.baseUrl + item.image : window.eShop.baseUrl + 'assets/no-image.png';
        var price = window.eShop.currencySymbol + parseFloat(item.price).toLocaleString();
        var totalPrice = window.eShop.currencySymbol + (parseFloat(item.price) * parseInt(item.qty)).toLocaleString();
        
        html += '<div class="cart-item" data-variant-id="' + item.product_variant_id + '">';
        html += '<div class="cart-item__content" style="display: flex; align-items: center;">';

        // Product Image
        html += '<div class="cart-item__media-wrapper">';
        html += '<img src="' + imageUrl + '" alt="' + item.name + '" class="cart-item__media">';
        html += '</div>';

        // Product Details
        html += '<div class="cart-item__details">';
        html += '<h3 class="cart-item__name">' + item.name + '</h3>';
        if (item.measurement) {
            html += '<p class="cart-item__variant">Size: ' + item.measurement + '</p>';
        }
        html += '<p class="cart-item__price">' + price + ' each</p>';

        // Quantity Selector
        html += '<div class="cart-item__quantity" style="margin: 1rem 0;">';
        html += '<div class="quantity-selector">';
        html += '<button type="button" class="quantity-decrease" data-variant-id="' + item.product_variant_id + '">';
        html += '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><path fill="currentColor" d="M4 8a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7A.5.5 0 0 1 4 8z"/></svg>';
        html += '</button>';
        html += '<input type="number" value="' + item.qty + '" min="1" class="quantity-input" data-variant-id="' + item.product_variant_id + '">';
        html += '<button type="button" class="quantity-increase" data-variant-id="' + item.product_variant_id + '">';
        html += '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><path fill="currentColor" d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/></svg>';
        html += '</button>';
        html += '</div>';
        html += '</div>';

        html += '</div>';

        // Item Total and Remove
        html += '<div class="cart-item__actions" style="text-align: right;">';
        html += '<p class="cart-item__total" style="font-weight: 600; margin-bottom: 1rem;">' + totalPrice + '</p>';
        html += '<button type="button" class="btn btn--tertiary cart-item__remove" data-variant-id="' + item.product_variant_id + '">Remove</button>';
        html += '</div>';

        html += '</div>';
        html += '</div>';
    });
    
    $('#cart-items').html(html);
}

function calculateCartTotals() {
    var subtotal = 0;
    var itemCount = 0;
    
    window.cartData.items.forEach(function(item) {
        subtotal += parseFloat(item.price) * parseInt(item.qty);
        itemCount += parseInt(item.qty);
    });
    
    window.cartData.subtotal = subtotal;
    window.cartData.total = subtotal; // Add shipping, tax, etc. later
    window.cartData.itemCount = itemCount;
    
    // Update UI
    $('#cart-item-count').text(itemCount);
    $('#cart-subtotal').text(window.eShop.currencySymbol + subtotal.toLocaleString());
    $('#cart-total').text(window.eShop.currencySymbol + window.cartData.total.toLocaleString());
    
    // Update header cart count
    $('.cart-count').text(itemCount);
    $('.cart-count-bubble').text(itemCount).toggle(itemCount > 0);
}

function updateCartItemQuantity(variantId, newQuantity) {
    window.eShop.cart.update(variantId, newQuantity, function(response) {
        if (response.error === false) {
            // Update local data
            var item = window.cartData.items.find(i => i.product_variant_id == variantId);
            if (item) {
                item.qty = newQuantity;
                calculateCartTotals();

                // Update item total display
                var totalPrice = window.eShop.currencySymbol + (parseFloat(item.price) * parseInt(newQuantity)).toLocaleString();
                $('[data-variant-id="' + variantId + '"]').find('.cart-item__total').text(totalPrice);
            }
        } else {
            window.eShop.cart.showNotification(response.message || 'Failed to update quantity', 'error');
            loadCartItems(); // Reload to get correct state
        }
    });
}

function removeCartItem(variantId) {
    if (confirm('Are you sure you want to remove this item from your cart?')) {
        window.eShop.cart.remove(variantId, function(response) {
            if (response.error === false) {
                // Remove from local data and UI
                window.cartData.items = window.cartData.items.filter(i => i.product_variant_id != variantId);
                $('[data-variant-id="' + variantId + '"]').remove();

                if (window.cartData.items.length === 0) {
                    $('#cart-content').hide();
                    $('#empty-cart').show();
                    $('#recommended-section').hide();
                } else {
                    calculateCartTotals();
                }
            }
        });
    }
}

function loadRecommendedProducts() {
    $.ajax({
        url: window.eShop.apiUrl + 'get_products',
        type: 'POST',
        data: {
            [window.eShop.csrfName]: window.eShop.csrfHash,
            limit: 4,
            sort: 'popular'
        },
        success: function(response) {
            if (response.error === false && response.data) {
                renderRecommendedProducts(response.data);
                $('#recommended-section').show();
            }
        }
    });
}

function renderRecommendedProducts(products) {
    var html = '';
    products.forEach(function(product, index) {
        var imageUrl = product.image ? window.eShop.baseUrl + product.image : window.eShop.baseUrl + 'assets/no-image.png';
        var price = window.eShop.currencySymbol + parseFloat(product.price).toLocaleString();
        var productUrl = window.eShop.baseUrl + 'products/details/' + product.slug;
        
        html += '<div class="grid__item scroll-trigger animate--slide-in" data-cascade style="--animation-order: ' + (index + 1) + ';">';
        html += '<div class="card-wrapper product-card-wrapper underline-links-hover">';
        html += '<div class="card card--standard card--media" style="--ratio-percent: 125.0%;">';
        html += '<div class="card__inner gradient ratio" style="--ratio-percent: 125.0%;">';
        html += '<div class="card__media">';
        html += '<div class="media media--transparent media--hover-effect">';
        html += '<img src="' + imageUrl + '" alt="' + product.name + '" class="motion-reduce" loading="lazy">';
        html += '</div>';
        html += '</div>';
        html += '<div class="card__content">';
        html += '<div class="card__information">';
        html += '<h3 class="card__heading h5">';
        html += '<a href="' + productUrl + '" class="full-unstyled-link">' + product.name + '</a>';
        html += '</h3>';
        html += '<div class="card-information">';
        html += '<span class="caption-large light">' + price + '</span>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
    });
    
    $('#recommended-products-grid').html(html);
}

function setupCartEventListeners() {
    // Quantity changes
    $(document).on('click', '.quantity-decrease', function() {
        var variantId = $(this).data('variant-id');
        var input = $(this).siblings('.quantity-input');
        var currentValue = parseInt(input.val()) || 1;

        if (currentValue > 1) {
            var newValue = currentValue - 1;
            input.val(newValue);
            updateCartItemQuantity(variantId, newValue);
        }
    });

    $(document).on('click', '.quantity-increase', function() {
        var variantId = $(this).data('variant-id');
        var input = $(this).siblings('.quantity-input');
        var currentValue = parseInt(input.val()) || 1;
        var newValue = currentValue + 1;

        input.val(newValue);
        updateCartItemQuantity(variantId, newValue);
    });

    $(document).on('change', '.quantity-input', function() {
        var variantId = $(this).data('variant-id');
        var newValue = parseInt($(this).val()) || 1;

        if (newValue < 1) {
            newValue = 1;
            $(this).val(newValue);
        }

        updateCartItemQuantity(variantId, newValue);
    });

    // Remove item
    $(document).on('click', '.cart-item__remove', function() {
        var variantId = $(this).data('variant-id');
        removeCartItem(variantId);
    });
    
    // Checkout button
    $(document).on('click', '#checkout-btn', function() {
        if (window.eShop.isLoggedIn) {
            window.location.href = window.eShop.baseUrl + 'checkout';
        } else {
            window.location.href = window.eShop.baseUrl + 'login?redirect=checkout';
        }
    });
    
    // Promo code form
    $(document).on('submit', '#promo-form', function(e) {
        e.preventDefault();
        var promoCode = $('#promo-code').val();
        
        if (promoCode) {
            // Apply promo code logic here
            window.eShop.cart.showNotification('Promo code functionality coming soon!', 'info');
        }
    });
}

// Initialize when document is ready
$(document).ready(function() {
    if (typeof initializeDynamicContent === 'function') {
        initializeDynamicContent();
    }
    initializeCart();
});
</script>
