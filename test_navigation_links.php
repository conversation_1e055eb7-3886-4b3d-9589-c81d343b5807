<?php
/**
 * Test Navigation Links
 * Verify all navigation links are working properly
 */

echo "<h1>🧭 Navigation Links Test</h1>";
echo "<style>
    body { font-family: 'Century Gothic', sans-serif; margin: 20px; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { color: #28a745; }
    .error { color: #dc3545; }
    .info { color: #17a2b8; }
    .test-item { margin: 10px 0; padding: 8px; background: #f8f9fa; border-radius: 3px; }
    .nav-link { display: inline-block; margin: 5px; padding: 10px 15px; background: #D4AF37; color: #000; text-decoration: none; border-radius: 5px; font-weight: 600; }
    .nav-link:hover { background: #B8941F; }
</style>";

$base_url = 'http://localhost/ritz';

echo "<div class='test-section'>";
echo "<h2>🏠 Main Navigation Links</h2>";

$main_links = [
    '' => 'Home',
    '/products' => 'SHOP / Products',
    '/about' => 'About',
    '/contact' => 'Contact',
    '/login' => 'Log in',
    '/cart' => 'Cart'
];

foreach ($main_links as $path => $name) {
    $url = $base_url . $path;
    echo "<div class='test-item'>";
    echo "<a href='$url' target='_blank' class='nav-link'>$name</a>";
    echo "<span class='info'> → $url</span>";
    echo "</div>";
}
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>📂 Category Links</h2>";

// Database connection to get categories
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'ritzm';

try {
    $conn = new mysqli($host, $username, $password, $database);
    
    if ($conn->connect_error) {
        echo "<div class='test-item error'>❌ Database connection failed</div>";
    } else {
        $categories_result = $conn->query("SELECT name, slug FROM categories WHERE status = 1 ORDER BY name");
        
        if ($categories_result && $categories_result->num_rows > 0) {
            while ($category = $categories_result->fetch_assoc()) {
                $category_url = $base_url . '/products?category=' . $category['slug'];
                echo "<div class='test-item'>";
                echo "<a href='$category_url' target='_blank' class='nav-link'>{$category['name']}</a>";
                echo "<span class='info'> → $category_url</span>";
                echo "</div>";
            }
        } else {
            echo "<div class='test-item error'>❌ No categories found</div>";
        }
    }
    
    $conn->close();
} catch (Exception $e) {
    echo "<div class='test-item error'>❌ Database error: " . $e->getMessage() . "</div>";
}

echo "</div>";

echo "<div class='test-section'>";
echo "<h2>🛍️ Product Links</h2>";

try {
    $conn = new mysqli($host, $username, $password, $database);
    
    if (!$conn->connect_error) {
        $products_result = $conn->query("SELECT name, slug FROM products WHERE status = 1 LIMIT 5");
        
        if ($products_result && $products_result->num_rows > 0) {
            while ($product = $products_result->fetch_assoc()) {
                $product_url = $base_url . '/products/details/' . $product['slug'];
                echo "<div class='test-item'>";
                echo "<a href='$product_url' target='_blank' class='nav-link'>{$product['name']}</a>";
                echo "<span class='info'> → $product_url</span>";
                echo "</div>";
            }
        } else {
            echo "<div class='test-item error'>❌ No products found</div>";
        }
    }
    
    $conn->close();
} catch (Exception $e) {
    echo "<div class='test-item error'>❌ Database error: " . $e->getMessage() . "</div>";
}

echo "</div>";

echo "<div class='test-section'>";
echo "<h2>🔧 Additional Pages</h2>";

$additional_links = [
    '/privacy-policy' => 'Privacy Policy',
    '/terms-and-conditions' => 'Terms & Conditions',
    '/shipping-and-returns' => 'Shipping & Returns',
    '/faqs' => 'FAQs',
    '/sizing' => 'Size Guide'
];

foreach ($additional_links as $path => $name) {
    $url = $base_url . $path;
    echo "<div class='test-item'>";
    echo "<a href='$url' target='_blank' class='nav-link'>$name</a>";
    echo "<span class='info'> → $url</span>";
    echo "</div>";
}
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>📊 Test Results Summary</h2>";
echo "<div class='test-item success'>✅ All navigation links have been generated</div>";
echo "<div class='test-item info'>ℹ️ Click each link to test if the page loads correctly</div>";
echo "<div class='test-item info'>🎯 Main navigation should work without errors</div>";
echo "<div class='test-item info'>📂 Category links should show filtered products</div>";
echo "<div class='test-item info'>🛍️ Product links should show individual product details</div>";
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>🚀 Quick Actions</h2>";
echo "<div class='test-item'>";
echo "<a href='$base_url' target='_blank' class='nav-link'>🏠 Go to Homepage</a>";
echo "<a href='$base_url/products' target='_blank' class='nav-link'>🛍️ Browse Products</a>";
echo "<a href='test_website_functionality.php' target='_blank' class='nav-link'>🧪 Full Website Test</a>";
echo "</div>";
echo "</div>";
?>
