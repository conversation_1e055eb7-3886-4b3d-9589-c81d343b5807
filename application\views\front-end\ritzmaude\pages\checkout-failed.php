<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<!-- Checkout Failed Page Content -->

<!-- Failed Header -->
<section class="shopify-section section">
    <style>
        .failed-header {
            padding-top: 56px;
            padding-bottom: 44px;
            text-align: center;
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }
        .failed-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem auto;
            font-size: 2.5rem;
        }
        .failed-header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .failed-header p {
            font-size: 1.25rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
    </style>
    <div class="failed-header">
        <div class="page-width">
            <div class="failed-icon scroll-trigger animate--fade-in">
                ✗
            </div>
            <h1 class="scroll-trigger animate--slide-in">Payment Failed</h1>
            <p class="scroll-trigger animate--slide-in">We're sorry, but there was an issue processing your payment. Please try again.</p>
        </div>
    </div>
</section>

<!-- Error Details -->
<section class="shopify-section section">
    <style>
        .section-error-padding {
            padding-top: 56px;
            padding-bottom: 44px;
        }
        .error-card {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            text-align: center;
            max-width: 600px;
            margin: 0 auto 2rem auto;
        }
        .error-reasons {
            background: rgba(var(--color-foreground), 0.02);
            padding: 2rem;
            border-radius: 8px;
            margin: 2rem 0;
            text-align: left;
        }
        .error-reasons h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            text-align: center;
        }
        .error-reasons ul {
            list-style: none;
            padding: 0;
        }
        .error-reasons li {
            padding: 0.75rem 0;
            border-bottom: 1px solid rgba(var(--color-foreground), 0.1);
            display: flex;
            align-items: flex-start;
        }
        .error-reasons li:last-child {
            border-bottom: none;
        }
        .error-reasons li::before {
            content: '•';
            color: #dc3545;
            font-weight: bold;
            margin-right: 0.75rem;
            margin-top: 0.25rem;
        }
        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }
        .btn {
            background: var(--primary-color);
            color: white;
            padding: 1rem 2rem;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-block;
        }
        .btn:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        .btn--secondary {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }
        .btn--secondary:hover {
            background: var(--primary-color);
            color: white;
        }
        .btn--danger {
            background: #dc3545;
        }
        .btn--danger:hover {
            background: #c82333;
        }
        .help-section {
            background: rgba(var(--color-foreground), 0.02);
            padding: 2rem;
            border-radius: 8px;
            margin-top: 3rem;
            text-align: center;
        }
        .help-section h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        .help-section p {
            color: rgba(var(--color-foreground), 0.7);
            margin-bottom: 1.5rem;
        }
    </style>
    
    <div class="color-scheme-1 gradient">
        <div class="page-width isolate section-error-padding">
            
            <!-- Error Message Card -->
            <div class="error-card scroll-trigger animate--slide-in">
                <h2 style="color: #dc3545; margin-bottom: 1rem;">Payment Could Not Be Processed</h2>
                <p style="margin-bottom: 2rem; color: rgba(var(--color-foreground), 0.7);">
                    Don't worry - your order hasn't been placed and you haven't been charged. 
                    You can try again or choose a different payment method.
                </p>
                
                <!-- Error Details -->
                <?php if (isset($_GET['error']) && !empty($_GET['error'])): ?>
                    <div style="background: #f8d7da; color: #721c24; padding: 1rem; border-radius: 4px; margin-bottom: 1.5rem;">
                        <strong>Error Details:</strong> <?= htmlspecialchars($_GET['error']) ?>
                    </div>
                <?php endif; ?>
                
                <!-- Primary Actions -->
                <div class="action-buttons">
                    <a href="<?= base_url('checkout') ?>" class="btn btn--danger">
                        Try Again
                    </a>
                    <a href="<?= base_url('cart') ?>" class="btn btn--secondary">
                        Review Cart
                    </a>
                </div>
            </div>

            <!-- Common Reasons -->
            <div class="error-reasons scroll-trigger animate--slide-in">
                <h3>Common Reasons for Payment Failure</h3>
                <ul>
                    <li>
                        <div>
                            <strong>Insufficient Funds:</strong> Your account may not have enough balance to complete the transaction.
                        </div>
                    </li>
                    <li>
                        <div>
                            <strong>Card Declined:</strong> Your bank may have declined the transaction for security reasons.
                        </div>
                    </li>
                    <li>
                        <div>
                            <strong>Incorrect Details:</strong> Please verify your card number, expiry date, and CVV are correct.
                        </div>
                    </li>
                    <li>
                        <div>
                            <strong>Network Issues:</strong> Temporary connectivity problems may have interrupted the payment.
                        </div>
                    </li>
                    <li>
                        <div>
                            <strong>Card Restrictions:</strong> Your card may have restrictions on online or international transactions.
                        </div>
                    </li>
                    <li>
                        <div>
                            <strong>Session Timeout:</strong> The payment session may have expired due to inactivity.
                        </div>
                    </li>
                </ul>
            </div>

            <!-- Alternative Options -->
            <div class="error-card scroll-trigger animate--slide-in">
                <h3 style="color: var(--primary-color); margin-bottom: 1.5rem;">Alternative Payment Options</h3>
                <p style="margin-bottom: 2rem; color: rgba(var(--color-foreground), 0.7);">
                    If you continue to experience issues, try one of these alternative payment methods:
                </p>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
                    <div style="padding: 1rem; background: rgba(var(--primary-color), 0.05); border-radius: 8px;">
                        <h4 style="margin-bottom: 0.5rem; color: var(--primary-color);">Cash on Delivery</h4>
                        <p style="margin: 0; font-size: 0.9rem;">Pay when your order is delivered</p>
                    </div>
                    
                    <div style="padding: 1rem; background: rgba(var(--primary-color), 0.05); border-radius: 8px;">
                        <h4 style="margin-bottom: 0.5rem; color: var(--primary-color);">Bank Transfer</h4>
                        <p style="margin: 0; font-size: 0.9rem;">Direct transfer to our account</p>
                    </div>
                    
                    <div style="padding: 1rem; background: rgba(var(--primary-color), 0.05); border-radius: 8px;">
                        <h4 style="margin-bottom: 0.5rem; color: var(--primary-color);">Different Card</h4>
                        <p style="margin: 0; font-size: 0.9rem;">Try with another payment card</p>
                    </div>
                </div>
                
                <div class="action-buttons">
                    <a href="<?= base_url('checkout') ?>" class="btn">
                        Choose Different Payment
                    </a>
                </div>
            </div>

            <!-- Help Section -->
            <div class="help-section scroll-trigger animate--slide-in">
                <h3>Still Having Issues?</h3>
                <p>
                    Our customer support team is ready to help you complete your purchase. 
                    Contact us and we'll resolve any payment issues quickly.
                </p>
                <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                    <a href="<?= base_url('contact') ?>" class="btn btn--secondary">Contact Support</a>
                    <a href="mailto:<?= isset($web_settings['support_email']) ? $web_settings['support_email'] : '<EMAIL>' ?>" class="btn btn--secondary">Email Us</a>
                    <?php if (isset($web_settings['support_phone']) && !empty($web_settings['support_phone'])): ?>
                        <a href="tel:<?= $web_settings['support_phone'] ?>" class="btn btn--secondary">Call Us</a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Cart Preservation Notice -->
            <div style="text-align: center; margin-top: 2rem; padding: 1.5rem; background: #d1ecf1; color: #0c5460; border-radius: 8px;" class="scroll-trigger animate--slide-in">
                <h4 style="margin-bottom: 0.5rem;">Your Cart is Safe</h4>
                <p style="margin: 0;">
                    Don't worry! All items in your cart have been saved. You can continue shopping or try checkout again anytime.
                </p>
            </div>

            <!-- Additional Actions -->
            <div class="action-buttons scroll-trigger animate--slide-in" style="margin-top: 3rem;">
                <a href="<?= base_url('products') ?>" class="btn btn--secondary">Continue Shopping</a>
                <a href="<?= base_url('my-account') ?>" class="btn btn--secondary">My Account</a>
                <a href="<?= base_url('/') ?>" class="btn btn--secondary">Back to Home</a>
            </div>
        </div>
    </div>
</section>

<!-- JavaScript for Failed Page -->
<script>
$(document).ready(function() {
    if (typeof initializeDynamicContent === 'function') {
        initializeDynamicContent();
    }
    
    // Track payment failure for analytics
    if (typeof gtag !== 'undefined') {
        gtag('event', 'payment_failed', {
            'event_category': 'ecommerce',
            'event_label': 'checkout_failure'
        });
    }
    
    // Auto-redirect to cart after 5 minutes of inactivity
    var redirectTimer = setTimeout(function() {
        if (confirm('Would you like to return to your cart to try again?')) {
            window.location.href = window.eShop.baseUrl + 'cart';
        }
    }, 300000); // 5 minutes
    
    // Clear timer if user interacts with page
    $(document).on('click scroll keypress', function() {
        clearTimeout(redirectTimer);
    });
});
</script>
