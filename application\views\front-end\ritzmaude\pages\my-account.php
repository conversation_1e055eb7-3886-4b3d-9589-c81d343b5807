<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<!-- My Account Page Content -->

<!-- Page Header -->
<section class="shopify-section section">
    <style>
        .page-header {
            padding-top: 42px;
            padding-bottom: 33px;
            text-align: center;
        }
        @media screen and (min-width: 750px) {
            .page-header {
                padding-top: 56px;
                padding-bottom: 44px;
            }
        }
    </style>
    <div class="page-header color-scheme-1 gradient">
        <div class="page-width">
            <h1 class="main-page-title page-title h0 scroll-trigger animate--fade-in">
                My Account
            </h1>
            <p class="page-description">Welcome back, <span id="user-name"><?= isset($user['first_name']) ? $user['first_name'] : 'Customer' ?></span>!</p>
        </div>
    </div>
</section>

<!-- Account Content -->
<section class="shopify-section section">
    <style>
        .section-account-padding {
            padding-top: 27px;
            padding-bottom: 27px;
        }
        @media screen and (min-width: 750px) {
            .section-account-padding {
                padding-top: 36px;
                padding-bottom: 44px;
            }
        }
        .account-nav {
            background: rgba(var(--color-foreground), 0.02);
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        .account-nav ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .account-nav li {
            margin-bottom: 0.5rem;
        }
        .account-nav a {
            display: block;
            padding: 0.75rem 1rem;
            text-decoration: none;
            color: rgba(var(--color-foreground), 0.75);
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        .account-nav a:hover,
        .account-nav a.active {
            background: var(--primary-color);
            color: white;
        }
        .account-content {
            background: rgba(var(--color-foreground), 0.02);
            padding: 2rem;
            border-radius: 8px;
            min-height: 400px;
        }
        .account-section {
            display: none;
        }
        .account-section.active {
            display: block;
        }
        .order-item {
            border: 1px solid rgba(var(--color-foreground), 0.1);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            background: white;
        }
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(var(--color-foreground), 0.1);
        }
        .order-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-confirmed { background: #d4edda; color: #155724; }
        .status-shipped { background: #cce5ff; color: #004085; }
        .status-delivered { background: #d1ecf1; color: #0c5460; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        .form-group {
            margin-bottom: 1.5rem;
        }
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid rgba(var(--color-foreground), 0.2);
            border-radius: 4px;
            font-size: 1rem;
        }
        .btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.3s ease;
        }
        .btn:hover {
            background: var(--secondary-color);
        }
        .btn-secondary {
            background: transparent;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }
        .btn-secondary:hover {
            background: var(--primary-color);
            color: white;
        }
    </style>
    
    <div class="color-scheme-1 gradient">
        <div class="page-width isolate section-account-padding">
            <div class="grid grid--1-col grid--2-col-tablet">
                
                <!-- Account Navigation -->
                <div class="grid__item">
                    <div class="account-nav">
                        <ul>
                            <li><a href="#" class="account-nav-link active" data-section="dashboard">Dashboard</a></li>
                            <li><a href="#" class="account-nav-link" data-section="orders">My Orders</a></li>
                            <li><a href="#" class="account-nav-link" data-section="addresses">Addresses</a></li>
                            <li><a href="#" class="account-nav-link" data-section="profile">Profile Settings</a></li>
                            <li><a href="#" class="account-nav-link" data-section="wishlist">Wishlist</a></li>
                            <li><a href="<?= base_url('customer-auth/logout') ?>" style="color: #dc3545;">Logout</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Account Content -->
                <div class="grid__item">
                    <div class="account-content">
                        
                        <!-- Dashboard Section -->
                        <div class="account-section active" id="dashboard">
                            <h2>Account Overview</h2>
                            <div class="grid grid--1-col grid--2-col-tablet" style="gap: 1rem; margin-top: 1.5rem;">
                                <div style="background: white; padding: 1.5rem; border-radius: 8px; text-align: center;">
                                    <h3 style="margin: 0 0 0.5rem 0; color: var(--primary-color);" id="total-orders">0</h3>
                                    <p style="margin: 0; color: rgba(var(--color-foreground), 0.7);">Total Orders</p>
                                </div>
                                <div style="background: white; padding: 1.5rem; border-radius: 8px; text-align: center;">
                                    <h3 style="margin: 0 0 0.5rem 0; color: var(--primary-color);" id="pending-orders">0</h3>
                                    <p style="margin: 0; color: rgba(var(--color-foreground), 0.7);">Pending Orders</p>
                                </div>
                            </div>
                            
                            <div style="margin-top: 2rem;">
                                <h3>Recent Orders</h3>
                                <div id="recent-orders">
                                    <!-- Recent orders will be loaded here -->
                                </div>
                            </div>
                        </div>

                        <!-- Orders Section -->
                        <div class="account-section" id="orders">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                                <h2>My Orders</h2>
                                <select id="order-filter" class="form-input" style="width: auto;">
                                    <option value="">All Orders</option>
                                    <option value="pending">Pending</option>
                                    <option value="confirmed">Confirmed</option>
                                    <option value="shipped">Shipped</option>
                                    <option value="delivered">Delivered</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>
                            <div id="orders-list">
                                <!-- Orders will be loaded here -->
                            </div>
                        </div>

                        <!-- Addresses Section -->
                        <div class="account-section" id="addresses">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                                <h2>My Addresses</h2>
                                <button class="btn" onclick="showAddAddressForm()">Add New Address</button>
                            </div>
                            <div id="addresses-list">
                                <!-- Addresses will be loaded here -->
                            </div>
                        </div>

                        <!-- Profile Section -->
                        <div class="account-section" id="profile">
                            <h2>Profile Settings</h2>
                            <form id="profile-form" style="margin-top: 1.5rem;">
                                <div class="grid grid--1-col grid--2-col-tablet" style="gap: 1rem;">
                                    <div class="form-group">
                                        <label class="form-label" for="first-name">First Name</label>
                                        <input type="text" id="first-name" name="first_name" class="form-input" value="<?= isset($user['first_name']) ? $user['first_name'] : '' ?>">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="last-name">Last Name</label>
                                        <input type="text" id="last-name" name="last_name" class="form-input" value="<?= isset($user['last_name']) ? $user['last_name'] : '' ?>">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label" for="email">Email</label>
                                    <input type="email" id="email" name="email" class="form-input" value="<?= isset($user['email']) ? $user['email'] : '' ?>">
                                </div>
                                <div class="form-group">
                                    <label class="form-label" for="mobile">Mobile Number</label>
                                    <input type="tel" id="mobile" name="mobile" class="form-input" value="<?= isset($user['mobile']) ? $user['mobile'] : '' ?>">
                                </div>
                                <button type="submit" class="btn">Update Profile</button>
                            </form>
                            
                            <hr style="margin: 2rem 0; border: none; border-top: 1px solid rgba(var(--color-foreground), 0.1);">
                            
                            <h3>Change Password</h3>
                            <form id="password-form" style="margin-top: 1rem;">
                                <div class="form-group">
                                    <label class="form-label" for="current-password">Current Password</label>
                                    <input type="password" id="current-password" name="current_password" class="form-input">
                                </div>
                                <div class="form-group">
                                    <label class="form-label" for="new-password">New Password</label>
                                    <input type="password" id="new-password" name="new_password" class="form-input">
                                </div>
                                <div class="form-group">
                                    <label class="form-label" for="confirm-password">Confirm New Password</label>
                                    <input type="password" id="confirm-password" name="confirm_password" class="form-input">
                                </div>
                                <button type="submit" class="btn">Change Password</button>
                            </form>
                        </div>

                        <!-- Wishlist Section -->
                        <div class="account-section" id="wishlist">
                            <h2>My Wishlist</h2>
                            <div id="wishlist-items" style="margin-top: 1.5rem;">
                                <!-- Wishlist items will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- JavaScript for Account Functionality -->
<script>
function initializeAccount() {
    setupAccountNavigation();
    loadAccountData();
    setupFormSubmissions();
}

function setupAccountNavigation() {
    $('.account-nav-link').on('click', function(e) {
        e.preventDefault();
        
        var section = $(this).data('section');
        
        // Update active nav item
        $('.account-nav-link').removeClass('active');
        $(this).addClass('active');
        
        // Show corresponding section
        $('.account-section').removeClass('active');
        $('#' + section).addClass('active');
        
        // Load section-specific data
        loadSectionData(section);
    });
}

function loadAccountData() {
    loadDashboardData();
}

function loadSectionData(section) {
    switch(section) {
        case 'orders':
            loadOrders();
            break;
        case 'addresses':
            loadAddresses();
            break;
        case 'wishlist':
            loadWishlist();
            break;
    }
}

function loadDashboardData() {
    // Load dashboard statistics
    $.ajax({
        url: window.eShop.apiUrl + 'get_user_orders',
        type: 'POST',
        data: {
            [window.eShop.csrfName]: window.eShop.csrfHash,
            limit: 5
        },
        success: function(response) {
            if (response.error === false && response.data) {
                $('#total-orders').text(response.total || response.data.length);
                var pendingCount = response.data.filter(order => order.status === 'pending').length;
                $('#pending-orders').text(pendingCount);
                
                renderRecentOrders(response.data.slice(0, 3));
            }
        }
    });
}

function renderRecentOrders(orders) {
    if (orders.length === 0) {
        $('#recent-orders').html('<p>No recent orders found.</p>');
        return;
    }
    
    var html = '';
    orders.forEach(function(order) {
        html += '<div class="order-item">';
        html += '<div class="order-header">';
        html += '<div>';
        html += '<strong>Order #' + order.id + '</strong>';
        html += '<br><small>' + formatDate(order.date_added) + '</small>';
        html += '</div>';
        html += '<span class="order-status status-' + order.status + '">' + capitalizeFirst(order.status) + '</span>';
        html += '</div>';
        html += '<p>Total: ' + window.eShop.currencySymbol + parseFloat(order.total).toLocaleString() + '</p>';
        html += '</div>';
    });
    
    $('#recent-orders').html(html);
}

function loadOrders() {
    var filter = $('#order-filter').val();
    var data = {
        [window.eShop.csrfName]: window.eShop.csrfHash
    };
    
    if (filter) {
        data.status = filter;
    }
    
    $.ajax({
        url: window.eShop.apiUrl + 'get_user_orders',
        type: 'POST',
        data: data,
        success: function(response) {
            if (response.error === false && response.data) {
                renderOrders(response.data);
            } else {
                $('#orders-list').html('<p>No orders found.</p>');
            }
        }
    });
}

function renderOrders(orders) {
    if (orders.length === 0) {
        $('#orders-list').html('<p>No orders found.</p>');
        return;
    }
    
    var html = '';
    orders.forEach(function(order) {
        html += '<div class="order-item">';
        html += '<div class="order-header">';
        html += '<div>';
        html += '<strong>Order #' + order.id + '</strong>';
        html += '<br><small>Placed on ' + formatDate(order.date_added) + '</small>';
        html += '</div>';
        html += '<span class="order-status status-' + order.status + '">' + capitalizeFirst(order.status) + '</span>';
        html += '</div>';
        html += '<div style="display: flex; justify-content: space-between; align-items: center;">';
        html += '<div>';
        html += '<p><strong>Total: ' + window.eShop.currencySymbol + parseFloat(order.total).toLocaleString() + '</strong></p>';
        html += '<p>Items: ' + (order.items ? order.items.length : 0) + '</p>';
        html += '</div>';
        html += '<div>';
        html += '<button class="btn btn-secondary" onclick="viewOrderDetails(' + order.id + ')">View Details</button>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
    });
    
    $('#orders-list').html(html);
}

function loadAddresses() {
    $.ajax({
        url: window.eShop.apiUrl + 'get_user_addresses',
        type: 'POST',
        data: {
            [window.eShop.csrfName]: window.eShop.csrfHash
        },
        success: function(response) {
            if (response.error === false && response.data) {
                renderAddresses(response.data);
            } else {
                $('#addresses-list').html('<p>No addresses found. <a href="#" onclick="showAddAddressForm()">Add your first address</a>.</p>');
            }
        }
    });
}

function renderAddresses(addresses) {
    var html = '';
    addresses.forEach(function(address) {
        html += '<div class="order-item">';
        html += '<div style="display: flex; justify-content: space-between; align-items: flex-start;">';
        html += '<div>';
        html += '<strong>' + address.type + ' Address</strong>';
        html += '<p>' + address.address + '<br>';
        html += address.city + ', ' + address.state + ' ' + address.pincode + '</p>';
        html += '</div>';
        html += '<div>';
        html += '<button class="btn btn-secondary" onclick="editAddress(' + address.id + ')">Edit</button>';
        html += '<button class="btn" style="background: #dc3545; margin-left: 0.5rem;" onclick="deleteAddress(' + address.id + ')">Delete</button>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
    });
    
    $('#addresses-list').html(html);
}

function loadWishlist() {
    $.ajax({
        url: window.eShop.apiUrl + 'get_favorites',
        type: 'POST',
        data: {
            [window.eShop.csrfName]: window.eShop.csrfHash
        },
        success: function(response) {
            if (response.error === false && response.data) {
                renderWishlist(response.data);
            } else {
                $('#wishlist-items').html('<p>Your wishlist is empty. <a href="' + window.eShop.baseUrl + 'products">Start shopping</a> to add items!</p>');
            }
        }
    });
}

function renderWishlist(items) {
    var html = '<div class="grid grid--2-col-tablet-down grid--3-col-desktop" style="gap: 1rem;">';
    
    items.forEach(function(item) {
        var imageUrl = item.image ? window.eShop.baseUrl + item.image : window.eShop.baseUrl + 'assets/no-image.png';
        var price = window.eShop.currencySymbol + parseFloat(item.price).toLocaleString();
        var productUrl = window.eShop.baseUrl + 'products/details/' + item.slug;
        
        html += '<div class="order-item">';
        html += '<img src="' + imageUrl + '" alt="' + item.name + '" style="width: 100%; height: 200px; object-fit: cover; border-radius: 4px; margin-bottom: 1rem;">';
        html += '<h4 style="margin: 0 0 0.5rem 0;"><a href="' + productUrl + '" style="text-decoration: none; color: inherit;">' + item.name + '</a></h4>';
        html += '<p style="margin: 0 0 1rem 0; font-weight: 600;">' + price + '</p>';
        html += '<div style="display: flex; gap: 0.5rem;">';
        html += '<button class="btn" style="flex: 1;" onclick="addToCartFromWishlist(' + item.product_id + ', ' + (item.variant_id || 'null') + ')">Add to Cart</button>';
        html += '<button class="btn btn-secondary" onclick="removeFromWishlist(' + item.id + ')">Remove</button>';
        html += '</div>';
        html += '</div>';
    });
    
    html += '</div>';
    $('#wishlist-items').html(html);
}

function setupFormSubmissions() {
    // Profile form
    $('#profile-form').on('submit', function(e) {
        e.preventDefault();
        
        var formData = $(this).serialize();
        
        $.ajax({
            url: window.eShop.baseUrl + 'my-account/update-profile',
            type: 'POST',
            data: formData + '&' + window.eShop.csrfName + '=' + window.eShop.csrfHash,
            dataType: 'json',
            success: function(response) {
                if (response.error === false) {
                    window.eShop.cart.showNotification('Profile updated successfully!', 'success');
                } else {
                    window.eShop.cart.showNotification(response.message || 'Failed to update profile', 'error');
                }
            }
        });
    });
    
    // Password form
    $('#password-form').on('submit', function(e) {
        e.preventDefault();
        
        var newPassword = $('#new-password').val();
        var confirmPassword = $('#confirm-password').val();
        
        if (newPassword !== confirmPassword) {
            window.eShop.cart.showNotification('Passwords do not match', 'error');
            return;
        }
        
        var formData = $(this).serialize();
        
        $.ajax({
            url: window.eShop.baseUrl + 'my-account/change-password',
            type: 'POST',
            data: formData + '&' + window.eShop.csrfName + '=' + window.eShop.csrfHash,
            dataType: 'json',
            success: function(response) {
                if (response.error === false) {
                    window.eShop.cart.showNotification('Password changed successfully!', 'success');
                    $('#password-form')[0].reset();
                } else {
                    window.eShop.cart.showNotification(response.message || 'Failed to change password', 'error');
                }
            }
        });
    });
    
    // Order filter
    $('#order-filter').on('change', function() {
        loadOrders();
    });
}

// Helper functions
function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString();
}

function capitalizeFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}

function viewOrderDetails(orderId) {
    window.location.href = window.eShop.baseUrl + 'my-account/order/' + orderId;
}

function showAddAddressForm() {
    // Implementation for add address form
    window.eShop.cart.showNotification('Add address functionality coming soon!', 'info');
}

function editAddress(addressId) {
    // Implementation for edit address
    window.eShop.cart.showNotification('Edit address functionality coming soon!', 'info');
}

function deleteAddress(addressId) {
    if (confirm('Are you sure you want to delete this address?')) {
        // Implementation for delete address
        window.eShop.cart.showNotification('Delete address functionality coming soon!', 'info');
    }
}

function addToCartFromWishlist(productId, variantId) {
    window.eShop.cart.addToCart(productId, variantId, 1);
}

function removeFromWishlist(wishlistId) {
    if (confirm('Remove this item from your wishlist?')) {
        $.ajax({
            url: window.eShop.apiUrl + 'remove_from_favorites',
            type: 'POST',
            data: {
                [window.eShop.csrfName]: window.eShop.csrfHash,
                id: wishlistId
            },
            success: function(response) {
                if (response.error === false) {
                    window.eShop.cart.showNotification('Item removed from wishlist', 'success');
                    loadWishlist();
                }
            }
        });
    }
}

// Initialize when document is ready
$(document).ready(function() {
    if (typeof initializeDynamicContent === 'function') {
        initializeDynamicContent();
    }
    initializeAccount();
});
</script>
