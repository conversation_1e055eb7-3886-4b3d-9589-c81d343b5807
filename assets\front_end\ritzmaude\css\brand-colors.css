/**
 * Ritz Maude Brand Colors - Black and Gold Theme
 * Updated to use black and gold color scheme as requested
 */

/* Root Variables for Brand Colors */
:root {
    --brand-black: #000000;
    --brand-gold: #D4AF37;
    --brand-gold-dark: #B8941F;
    --brand-gold-light: #E6C55A;
    --brand-dark-gray: #333333;
    --brand-light-gray: #f5f5f5;
    --brand-white: #ffffff;
    --brand-off-white: #fafafa;
}

/* Primary Color Overrides */
.color-scheme-1 {
    --color-foreground: 0, 0, 0;
    --color-background: 255, 255, 255;
    --color-link: 212, 175, 55; /* Gold for links */
    --color-button: 212, 175, 55; /* Gold buttons */
    --color-button-text: 0, 0, 0; /* Black text on gold */
}

.color-scheme-2 {
    --color-foreground: 255, 255, 255;
    --color-background: 0, 0, 0; /* Black Background */
    --color-link: 212, 175, 55; /* Gold for links on black */
    --color-button: 212, 175, 55; /* Gold buttons on black background */
    --color-button-text: 0, 0, 0; /* Black text on gold */
}

/* Button Styling */
.button,
.btn,
.shopify-payment-button__button--unbranded {
    background-color: var(--brand-gold) !important;
    color: var(--brand-black) !important;
    border-color: var(--brand-gold) !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

.button:hover,
.btn:hover,
.shopify-payment-button__button--unbranded:hover {
    background-color: var(--brand-gold-dark) !important;
    color: var(--brand-black) !important;
    transform: translateY(-1px) !important;
}

.button--secondary {
    background-color: transparent !important;
    color: var(--brand-gold) !important;
    border: 2px solid var(--brand-gold) !important;
}

.button--secondary:hover {
    background-color: var(--brand-gold) !important;
    color: var(--brand-black) !important;
}

/* Link Colors */
a,
.link {
    color: var(--brand-gold);
    transition: color 0.3s ease;
}

a:hover,
.link:hover {
    color: var(--brand-gold-dark);
}

/* Header Styling */
.header {
    background-color: var(--brand-white);
    border-bottom: 1px solid var(--brand-light-gray);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header__icon {
    color: var(--brand-black);
    transition: color 0.3s ease;
}

.header__icon:hover {
    color: var(--brand-gold);
}

/* Cart Count Bubble */
.cart-count-bubble {
    background-color: var(--brand-gold) !important;
    color: var(--brand-black) !important;
    font-size: 0.75rem !important;
    width: 18px !important;
    height: 18px !important;
    bottom: 0.5rem !important;
    left: 1.8rem !important;
    font-weight: 600 !important;
}

/* Cart Icon Sizing */
.header__icon--cart {
    width: 44px !important;
    height: 44px !important;
}

.header__icon--cart .icon {
    width: 20px !important;
    height: 20px !important;
}

/* Navigation */
.header__menu-item,
.list-menu__item--link {
    color: var(--brand-black);
    transition: color 0.3s ease;
}

.header__menu-item:hover,
.list-menu__item--link:hover {
    color: var(--brand-gold);
}

/* Footer Styling */
.footer {
    background-color: var(--brand-black) !important;
    color: var(--brand-white) !important;
}

.footer .link--text {
    color: var(--brand-white) !important;
    transition: color 0.3s ease !important;
}

.footer .link--text:hover {
    color: var(--brand-gold) !important;
}

.footer-block__heading {
    color: var(--brand-white) !important;
    font-weight: 600 !important;
}

/* Form Elements */
.field__input,
.select__select,
.text-area {
    border-color: var(--brand-light-gray);
    background-color: var(--brand-white);
    color: var(--brand-black);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.field__input:focus,
.select__select:focus,
.text-area:focus {
    border-color: var(--brand-gold);
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
    outline: none;
}

.field__label {
    color: var(--brand-black);
}

/* Newsletter Form */
.newsletter-form__button {
    background-color: var(--brand-gold) !important;
    color: var(--brand-black) !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

.newsletter-form__button:hover {
    background-color: var(--brand-gold-dark) !important;
    transform: translateY(-1px) !important;
}

/* Product Cards */
.card-wrapper .card {
    border: 1px solid var(--brand-light-gray);
    transition: all 0.3s ease;
    background-color: var(--brand-white);
}

.card-wrapper .card:hover {
    border-color: var(--brand-gold);
    box-shadow: 0 8px 16px rgba(212, 175, 55, 0.15);
    transform: translateY(-2px);
}

.card__heading a {
    color: var(--brand-black);
    transition: color 0.3s ease;
}

.card__heading a:hover {
    color: var(--brand-gold);
}

.price {
    color: var(--brand-gold);
    font-weight: 600;
}

.price-item--sale {
    color: var(--brand-gold);
}

.price-item--regular {
    color: var(--brand-dark-gray);
}

/* Add to Cart Buttons */
.product-form__cart-submit,
.add-to-cart-btn {
    background-color: var(--brand-gold) !important;
    color: var(--brand-black) !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

.product-form__cart-submit:hover,
.add-to-cart-btn:hover {
    background-color: var(--brand-gold-dark) !important;
    transform: translateY(-1px) !important;
}

/* Quantity Selector */
.quantity {
    border-color: var(--brand-light-gray);
}

.quantity__button {
    color: var(--brand-black);
    transition: all 0.3s ease;
}

.quantity__button:hover {
    background-color: var(--brand-gold);
    color: var(--brand-black);
}

.quantity__input {
    color: var(--brand-black);
}

/* Badges and Labels */
.badge {
    background-color: var(--brand-gold);
    color: var(--brand-black);
    font-weight: 600;
}

.badge--sale {
    background-color: var(--brand-gold);
    color: var(--brand-black);
}

/* Pagination */
.pagination__item {
    border-color: var(--brand-gold);
}

.pagination__item--current {
    background-color: var(--brand-gold);
    color: var(--brand-black);
}

.pagination__item:hover {
    background-color: var(--brand-gold);
    color: var(--brand-black);
}

/* Breadcrumbs */
.breadcrumb__link {
    color: var(--brand-gold);
}

.breadcrumb__link:hover {
    color: var(--brand-gold-dark);
}

/* Filters and Sorting */
.facets__disclosure {
    border-color: var(--brand-gold);
}

.facets__summary {
    color: var(--brand-gold);
}

/* Collection Hero */
.collection-hero__title {
    color: var(--brand-black);
}

.collection-hero__description {
    color: var(--brand-dark-gray);
}

/* Search */
.search__button {
    color: var(--brand-gold);
}

.search__input:focus {
    border-color: var(--brand-gold);
}

/* Notifications */
.notification {
    border-left: 4px solid var(--brand-gold);
}

.notification--success {
    background-color: rgba(212, 175, 55, 0.1);
    border-left-color: var(--brand-gold);
}

.notification--error {
    background-color: rgba(220, 53, 69, 0.1);
    border-left-color: #dc3545;
}

/* Loading States */
.loading__spinner .path {
    stroke: var(--brand-gold);
}

/* Focus States */
*:focus-visible {
    outline-color: var(--brand-gold);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.3);
}

/* Mobile Menu */
.menu-drawer {
    background-color: var(--brand-white);
}

.menu-drawer__menu-item {
    color: var(--brand-black);
}

.menu-drawer__menu-item:hover {
    color: var(--brand-gold);
    background-color: rgba(212, 175, 55, 0.1);
}

/* Announcement Bar */
.announcement-bar {
    background-color: var(--brand-gold);
    color: var(--brand-black);
}

.announcement-bar__link {
    color: var(--brand-black);
}

.announcement-bar__link:hover {
    color: var(--brand-white);
}

/* Social Media Icons */
.list-social__link {
    color: var(--brand-gold);
}

.list-social__link:hover {
    color: var(--brand-gold-dark);
}

/* Override any remaining red/wine colors */
.text-red,
.color-red,
.bg-red {
    color: var(--brand-gold) !important;
    background-color: var(--brand-gold) !important;
}

/* Ensure proper contrast */
.text-on-gold {
    color: var(--brand-black) !important;
}

.text-on-black {
    color: var(--brand-white) !important;
}

/* Login and Registration Forms */
.customer .field input,
.customer select {
    border-color: var(--brand-light-gray);
    background-color: var(--brand-white);
    color: var(--brand-black);
}

.customer .field input:focus,
.customer select:focus {
    border-color: var(--brand-gold);
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

.customer button {
    background-color: var(--brand-gold) !important;
    color: var(--brand-black) !important;
    font-weight: 600 !important;
}

.customer button:hover {
    background-color: var(--brand-gold-dark) !important;
}

/* Cart Page Styling */
.cart-item {
    border-bottom: 1px solid var(--brand-light-gray);
}

.cart-item__name a {
    color: var(--brand-black);
}

.cart-item__name a:hover {
    color: var(--brand-gold);
}

.cart-item__remove {
    color: var(--brand-gold);
}

.cart-item__remove:hover {
    color: var(--brand-gold-dark);
}

/* Custom animations with brand colors */
@keyframes goldPulse {
    0% { box-shadow: 0 0 0 0 rgba(212, 175, 55, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(212, 175, 55, 0); }
    100% { box-shadow: 0 0 0 0 rgba(212, 175, 55, 0); }
}

.pulse-gold {
    animation: goldPulse 2s infinite;
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
    .cart-count-bubble {
        width: 16px !important;
        height: 16px !important;
        font-size: 0.7rem !important;
    }

    .header__icon--cart {
        width: 40px !important;
        height: 40px !important;
    }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
    .card-wrapper .card {
        background-color: var(--brand-off-white);
    }
}
