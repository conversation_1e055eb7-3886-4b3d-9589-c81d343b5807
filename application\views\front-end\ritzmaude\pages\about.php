<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<!-- About Page Content -->

<!-- Hero Section -->
<section class="shopify-section section">
    <style>
        .about-hero {
            padding-top: 42px;
            padding-bottom: 33px;
            text-align: center;
            background: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), url('<?= base_url('assets/front_end/Ritz Maude Frontpage/images/files-ANDREW_TABITHA_2025_034.jpg') ?>');
            background-size: cover;
            background-position: center;
            color: white;
            min-height: 60vh;
            display: flex;
            align-items: center;
        }
        @media screen and (min-width: 750px) {
            .about-hero {
                padding-top: 56px;
                padding-bottom: 44px;
                min-height: 70vh;
            }
        }
        .about-hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .about-hero p {
            font-size: 1.25rem;
            max-width: 600px;
            margin: 0 auto;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
    </style>
    <div class="about-hero">
        <div class="page-width">
            <h1 class="scroll-trigger animate--fade-in">RITZ MAUDE</h1>
            <p class="scroll-trigger animate--slide-in">At Ritz Maude we honour your uniqueness by crafting pieces that celebrate the male and female form</p>
        </div>
    </div>
</section>

<!-- Brand Story Section -->
<section class="shopify-section section">
    <style>
        .section-story-padding {
            padding-top: 56px;
            padding-bottom: 44px;
        }
        .story-content {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .story-content h2 {
            font-size: 2.5rem;
            margin-bottom: 2rem;
            color: var(--primary-color);
        }
        .story-content p {
            font-size: 1.125rem;
            line-height: 1.8;
            margin-bottom: 1.5rem;
            color: rgba(var(--color-foreground), 0.8);
        }
    </style>
    <div class="color-scheme-1 gradient">
        <div class="page-width isolate section-story-padding">
            <div class="story-content scroll-trigger animate--slide-in">
                <h2>Our Story</h2>
                <p>
                    Ritz Maude was born from a vision to create fashion that transcends conventional boundaries. 
                    We believe that clothing should be an extension of your personality, a canvas for your individuality, 
                    and a celebration of your unique story.
                </p>
                <p>
                    Our journey began with a simple yet powerful idea: to design pieces that honor both the masculine 
                    and feminine essence within us all. Every garment we create is thoughtfully crafted to embrace 
                    the complexity and beauty of human expression.
                </p>
                <p>
                    From our signature "Gracefully Broken" collection to our street-inspired pieces, each design 
                    tells a story of resilience, authenticity, and the courage to be unapologetically yourself.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Values Section -->
<section class="shopify-section section">
    <style>
        .section-values-padding {
            padding-top: 56px;
            padding-bottom: 44px;
            background: rgba(var(--color-foreground), 0.02);
        }
        .values-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        .value-card {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .value-card:hover {
            transform: translateY(-5px);
        }
        .value-icon {
            width: 60px;
            height: 60px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem auto;
            color: white;
            font-size: 1.5rem;
        }
        .value-card h3 {
            margin-bottom: 1rem;
            color: var(--primary-color);
        }
        .value-card p {
            color: rgba(var(--color-foreground), 0.7);
            line-height: 1.6;
        }
    </style>
    <div class="color-scheme-1 gradient section-values-padding">
        <div class="page-width isolate">
            <div class="text-center scroll-trigger animate--slide-in">
                <h2 style="font-size: 2.5rem; margin-bottom: 1rem; color: var(--primary-color);">Our Values</h2>
                <p style="font-size: 1.125rem; max-width: 600px; margin: 0 auto; color: rgba(var(--color-foreground), 0.8);">
                    These core principles guide everything we do, from design to delivery
                </p>
            </div>
            
            <div class="values-grid">
                <div class="value-card scroll-trigger animate--slide-in" data-cascade style="--animation-order: 1;">
                    <div class="value-icon">✨</div>
                    <h3>Authenticity</h3>
                    <p>We believe in staying true to our vision and creating pieces that reflect genuine artistic expression, not fleeting trends.</p>
                </div>
                
                <div class="value-card scroll-trigger animate--slide-in" data-cascade style="--animation-order: 2;">
                    <div class="value-icon">🌍</div>
                    <h3>Sustainability</h3>
                    <p>Our commitment to the environment drives us to use sustainable materials and ethical production practices.</p>
                </div>
                
                <div class="value-card scroll-trigger animate--slide-in" data-cascade style="--animation-order: 3;">
                    <div class="value-icon">🎨</div>
                    <h3>Craftsmanship</h3>
                    <p>Every piece is meticulously crafted with attention to detail, ensuring quality that stands the test of time.</p>
                </div>
                
                <div class="value-card scroll-trigger animate--slide-in" data-cascade style="--animation-order: 4;">
                    <div class="value-icon">💫</div>
                    <h3>Inclusivity</h3>
                    <p>Fashion should be for everyone. We design for all bodies, all identities, and all expressions of self.</p>
                </div>
                
                <div class="value-card scroll-trigger animate--slide-in" data-cascade style="--animation-order: 5;">
                    <div class="value-icon">🚀</div>
                    <h3>Innovation</h3>
                    <p>We constantly push boundaries, experimenting with new techniques and materials to create something extraordinary.</p>
                </div>
                
                <div class="value-card scroll-trigger animate--slide-in" data-cascade style="--animation-order: 6;">
                    <div class="value-icon">❤️</div>
                    <h3>Community</h3>
                    <p>We're more than a brand; we're a community of individuals who celebrate uniqueness and support each other.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Collections Showcase -->
<section class="shopify-section section">
    <style>
        .section-collections-padding {
            padding-top: 56px;
            padding-bottom: 44px;
        }
        .collections-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        .collection-card {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            height: 400px;
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: flex-end;
            transition: transform 0.3s ease;
        }
        .collection-card:hover {
            transform: scale(1.02);
        }
        .collection-overlay {
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            width: 100%;
            padding: 2rem;
            color: white;
        }
        .collection-card h3 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }
        .collection-card p {
            margin-bottom: 1rem;
            opacity: 0.9;
        }
        .collection-card .btn {
            background: white;
            color: var(--primary-color);
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .collection-card .btn:hover {
            background: var(--primary-color);
            color: white;
        }
    </style>
    <div class="color-scheme-1 gradient">
        <div class="page-width isolate section-collections-padding">
            <div class="text-center scroll-trigger animate--slide-in">
                <h2 style="font-size: 2.5rem; margin-bottom: 1rem; color: var(--primary-color);">Our Collections</h2>
                <p style="font-size: 1.125rem; max-width: 600px; margin: 0 auto; color: rgba(var(--color-foreground), 0.8);">
                    Explore our carefully curated collections, each telling its own unique story
                </p>
            </div>
            
            <div class="collections-showcase">
                <div class="collection-card scroll-trigger animate--slide-in" 
                     style="background-image: url('<?= base_url('assets/front_end/Ritz Maude Frontpage/images/collections-ANDREW_TABITHA_2025_036A.jpg') ?>');"
                     data-cascade style="--animation-order: 1;">
                    <div class="collection-overlay">
                        <h3>Gracefully Broken</h3>
                        <p>A quiet rebellion against perfection; the unraveling of conventional beauty standards.</p>
                        <a href="<?= base_url('products?collection=gracefully-broken') ?>" class="btn">Explore Collection</a>
                    </div>
                </div>
                
                <div class="collection-card scroll-trigger animate--slide-in" 
                     style="background-image: url('<?= base_url('assets/front_end/Ritz Maude Frontpage/images/collections-AXR_6002crop.jpg') ?>');"
                     data-cascade style="--animation-order: 2;">
                    <div class="collection-overlay">
                        <h3>Street Culture</h3>
                        <p>Urban-inspired pieces that celebrate the raw energy and authenticity of street fashion.</p>
                        <a href="<?= base_url('products?collection=street-culture') ?>" class="btn">Explore Collection</a>
                    </div>
                </div>
                
                <div class="collection-card scroll-trigger animate--slide-in" 
                     style="background-image: url('<?= base_url('assets/front_end/Ritz Maude Frontpage/images/collections-AXR_5999crop2_1.jpg') ?>');"
                     data-cascade style="--animation-order: 3;">
                    <div class="collection-overlay">
                        <h3>Roots</h3>
                        <p>Celebrating our heritage with traditional patterns reimagined for the modern world.</p>
                        <a href="<?= base_url('products?collection=roots') ?>" class="btn">Explore Collection</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Team Section -->
<section class="shopify-section section">
    <style>
        .section-team-padding {
            padding-top: 56px;
            padding-bottom: 44px;
            background: rgba(var(--color-foreground), 0.02);
        }
        .team-content {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .founders-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        .founder-card {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .founder-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: var(--primary-color);
            margin: 0 auto 1rem auto;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: bold;
        }
        .founder-card h3 {
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }
        .founder-card .title {
            color: rgba(var(--color-foreground), 0.6);
            font-style: italic;
            margin-bottom: 1rem;
        }
        .founder-card p {
            color: rgba(var(--color-foreground), 0.7);
            line-height: 1.6;
        }
    </style>
    <div class="color-scheme-1 gradient section-team-padding">
        <div class="page-width isolate">
            <div class="team-content scroll-trigger animate--slide-in">
                <h2 style="font-size: 2.5rem; margin-bottom: 1rem; color: var(--primary-color);">Meet the Visionaries</h2>
                <p style="font-size: 1.125rem; color: rgba(var(--color-foreground), 0.8);">
                    The creative minds behind Ritz Maude, dedicated to redefining fashion and celebrating individuality.
                </p>
            </div>
            
            <div class="founders-grid">
                <div class="founder-card scroll-trigger animate--slide-in" data-cascade style="--animation-order: 1;">
                    <div class="founder-image">A</div>
                    <h3>Andrew</h3>
                    <div class="title">Creative Director & Co-Founder</div>
                    <p>Visionary designer with a passion for breaking conventional fashion boundaries and creating pieces that tell stories.</p>
                </div>
                
                <div class="founder-card scroll-trigger animate--slide-in" data-cascade style="--animation-order: 2;">
                    <div class="founder-image">T</div>
                    <h3>Tabitha</h3>
                    <div class="title">Design Director & Co-Founder</div>
                    <p>Master craftsperson dedicated to sustainable fashion and empowering individuals through thoughtful design.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="shopify-section section">
    <style>
        .section-cta-padding {
            padding-top: 56px;
            padding-bottom: 44px;
        }
        .cta-content {
            text-align: center;
            max-width: 600px;
            margin: 0 auto;
        }
        .cta-content h2 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }
        .cta-content p {
            font-size: 1.125rem;
            margin-bottom: 2rem;
            color: rgba(var(--color-foreground), 0.8);
        }
        .cta-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        .btn {
            background: var(--primary-color);
            color: white;
            padding: 1rem 2rem;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }
        .btn-secondary {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }
        .btn-secondary:hover {
            background: var(--primary-color);
            color: white;
        }
    </style>
    <div class="color-scheme-1 gradient">
        <div class="page-width isolate section-cta-padding">
            <div class="cta-content scroll-trigger animate--slide-in">
                <h2>Join the Ritz Maude Community</h2>
                <p>
                    Ready to embrace your uniqueness? Explore our collections and find pieces that celebrate who you are.
                </p>
                <div class="cta-buttons">
                    <a href="<?= base_url('products') ?>" class="btn">Shop Now</a>
                    <a href="<?= base_url('contact') ?>" class="btn btn-secondary">Get in Touch</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- JavaScript for About Page -->
<script>
$(document).ready(function() {
    if (typeof initializeDynamicContent === 'function') {
        initializeDynamicContent();
    }
    
    // Add smooth scrolling for any internal links
    $('a[href^="#"]').on('click', function(e) {
        e.preventDefault();
        var target = $(this.getAttribute('href'));
        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });
});
</script>
