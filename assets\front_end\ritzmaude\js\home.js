/**
 * Ritz Maude Home Page JavaScript
 * Handles dynamic content loading for the home page
 */

$(document).ready(function() {
    // Load featured products on page load
    loadFeaturedProducts();
    
    // Initialize any other home page functionality
    initializeHomePage();
});

/**
 * Load featured products dynamically
 */
function loadFeaturedProducts() {
    const featuredGrid = $('#featured-products-grid');
    
    // Show loading state
    featuredGrid.html(`
        <div class="loading-container" style="grid-column: 1 / -1; text-align: center; padding: 2rem;">
            <div class="loading-spinner"></div>
            <p>Loading featured products...</p>
        </div>
    `);
    
    // Make API call to get featured products
    $.ajax({
        url: window.location.origin + '/ritz/app/v1/api/get_products',
        type: 'POST',
        data: {
            featured: 1,
            limit: 8,
            offset: 0
        },
        dataType: 'json',
        success: function(response) {
            if (response.error === false && response.data && response.data.length > 0) {
                displayFeaturedProducts(response.data);
            } else {
                showNoProductsMessage();
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading featured products:', error);
            showErrorMessage();
        }
    });
}

/**
 * Display featured products in the grid
 */
function displayFeaturedProducts(products) {
    const featuredGrid = $('#featured-products-grid');
    let productsHTML = '';
    
    products.forEach(function(product, index) {
        const animationOrder = index + 1;
        const salePrice = product.sale_price && parseFloat(product.sale_price) > 0 ? parseFloat(product.sale_price) : null;
        const regularPrice = parseFloat(product.price);
        const hasDiscount = salePrice && salePrice < regularPrice;
        
        productsHTML += `
            <div class="grid__item scroll-trigger animate--slide-in" data-cascade style="--animation-order: ${animationOrder};">
                <div class="card-wrapper product-card-wrapper underline-links-hover">
                    <div class="card card--standard card--media" style="--ratio-percent: 125.0%;">
                        <div class="card__inner color-scheme-2 gradient ratio" style="--ratio-percent: 125.0%;">
                            <div class="card__media">
                                <div class="media media--transparent media--hover-effect">
                                    <img src="${window.location.origin}/ritz/${product.image}" 
                                         alt="${product.name}" 
                                         class="motion-reduce"
                                         loading="lazy"
                                         style="object-fit: cover; width: 100%; height: 100%;">
                                </div>
                            </div>
                            <div class="card__content">
                                <div class="card__information">
                                    <h3 class="card__heading">
                                        <a href="${window.location.origin}/ritz/products/details/${product.slug}" class="full-unstyled-link">
                                            ${product.name}
                                        </a>
                                    </h3>
                                    <div class="card-information">
                                        <span class="caption-large light">${product.short_description || ''}</span>
                                        <div class="price">
                                            ${hasDiscount ? 
                                                `<div class="price__container">
                                                    <span class="price-item price-item--sale price-item--last">
                                                        ₦${formatPrice(salePrice)}
                                                    </span>
                                                    <span class="price-item price-item--regular">
                                                        ₦${formatPrice(regularPrice)}
                                                    </span>
                                                </div>` :
                                                `<div class="price__container">
                                                    <span class="price-item price-item--regular">
                                                        ₦${formatPrice(regularPrice)}
                                                    </span>
                                                </div>`
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="card__badge bottom left">
                                    ${hasDiscount ? '<span class="badge badge--bottom-left color-scheme-4">Sale</span>' : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    featuredGrid.html(productsHTML);
}

/**
 * Show message when no products are found
 */
function showNoProductsMessage() {
    const featuredGrid = $('#featured-products-grid');
    featuredGrid.html(`
        <div class="no-products-message" style="grid-column: 1 / -1; text-align: center; padding: 3rem;">
            <h3>No Featured Products</h3>
            <p>Check back soon for our latest featured products.</p>
            <a href="${window.location.origin}/ritz/products" class="button button--primary">
                View All Products
            </a>
        </div>
    `);
}

/**
 * Show error message
 */
function showErrorMessage() {
    const featuredGrid = $('#featured-products-grid');
    featuredGrid.html(`
        <div class="error-message" style="grid-column: 1 / -1; text-align: center; padding: 3rem;">
            <h3>Unable to Load Products</h3>
            <p>Please try refreshing the page.</p>
            <button onclick="loadFeaturedProducts()" class="button button--secondary">
                Try Again
            </button>
        </div>
    `);
}

/**
 * Format price for display
 */
function formatPrice(price) {
    return parseFloat(price).toLocaleString('en-NG', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    });
}

/**
 * Initialize other home page functionality
 */
function initializeHomePage() {
    // Add any additional home page initialization here
    
    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });
    
    // Initialize any carousel or slider functionality
    initializeCarousels();
}

/**
 * Initialize carousels and sliders
 */
function initializeCarousels() {
    // Add carousel initialization if needed
    console.log('Carousels initialized');
}

// Export functions for global access
window.RitzMaude = window.RitzMaude || {};
window.RitzMaude.loadFeaturedProducts = loadFeaturedProducts;
window.RitzMaude.formatPrice = formatPrice;
