<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<!-- Ritz Maude Product Listing Page -->
<div class="section-product-listing section-sections--22128063250748__product-listing-padding">
    
    <!-- Breadcrumb -->
    <div class="page-width">
        <nav class="breadcrumb" role="navigation" aria-label="breadcrumbs">
            <a href="<?= base_url() ?>" class="breadcrumb__link">Home</a>
            <?php if (isset($right_breadcrumb) && !empty($right_breadcrumb)): ?>
                <?php foreach ($right_breadcrumb as $crumb): ?>
                    <span class="breadcrumb__separator" aria-hidden="true">/</span>
                    <span class="breadcrumb__text"><?= $crumb ?></span>
                <?php endforeach; ?>
            <?php endif; ?>
            <span class="breadcrumb__separator" aria-hidden="true">/</span>
            <span class="breadcrumb__text" aria-current="page">
                <?= isset($page_main_bread_crumb) ? $page_main_bread_crumb : 'Products' ?>
            </span>
        </nav>
    </div>

    <!-- Hidden inputs for filters -->
    <input type="hidden" id="product-filters" value='<?= (!empty($filters)) ? escape_array($filters) : "" ?>' data-key="<?= $filters_key ?>" />
    <input type="hidden" id="brand-filters" value='<?= (!empty($brands)) ? escape_array($brands) : "" ?>' data-key="<?= $filters_key ?>" />
    <input type="hidden" id="category-filters" value='<?= (!empty($categories) ? ($categories) : "") ?>' data-key="<?= $filters_key ?>" />

    <!-- Main Content -->
    <div class="page-width">
        
        <!-- Page Header -->
        <div class="collection-hero">
            <div class="collection-hero__inner">
                <h1 class="collection-hero__title">
                    <?= isset($page_main_bread_crumb) ? $page_main_bread_crumb : 'Products' ?>
                    <?= (isset($seller) && !empty($seller[0]['store_name'])) ? " By " . $seller[0]['store_name'] : '' ?>
                </h1>
                <?php if (isset($collection_description) && !empty($collection_description)): ?>
                    <div class="collection-hero__description rte">
                        <?= $collection_description ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Filters and Products Grid -->
        <div class="collection-product-wrapper">
            
            <!-- Filter Toggle (Mobile) -->
            <div class="collection-filters-wrapper">
                <details class="disclosure-has-popup facets-container" id="Details-FilterDrawer">
                    <summary class="facets__disclosure" aria-label="Filter and sort">
                        <span class="facets__summary">
                            <svg aria-hidden="true" focusable="false" class="icon icon-filter" viewBox="0 0 20 20">
                                <path fill="currentColor" d="M8.5 2.75a.75.75 0 0 0-1.5 0v6.5H3.204a2 2 0 0 0 0 4H7v4.5a.75.75 0 0 0 1.5 0V13h3.796a2 2 0 0 0 0-4H8.5v-6.5Z"/>
                            </svg>
                            Filter and sort
                        </span>
                        <svg aria-hidden="true" focusable="false" class="icon icon-caret" viewBox="0 0 10 6">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M9.354.646a.5.5 0 0 0-.708 0L5 4.293 1.354.646a.5.5 0 0 0-.708.708l4 4a.5.5 0 0 0 .708 0l4-4a.5.5 0 0 0 0-.708z" fill="currentColor"/>
                        </svg>
                    </summary>
                    
                    <!-- Filter Content -->
                    <div class="facets__form">
                        <form id="FacetFiltersForm" class="facets">
                            
                            <!-- Attribute Filters -->
                            <?php if (isset($products['filters']) && !empty($products['filters'])): ?>
                                <?php foreach ($products['filters'] as $key => $filter): ?>
                                    <details class="disclosure-has-popup facets__disclosure js-filter" data-index="<?= $key ?>">
                                        <summary class="facets__summary">
                                            <div>
                                                <span><?= html_escape($filter['name']) ?></span>
                                                <svg aria-hidden="true" focusable="false" class="icon icon-caret" viewBox="0 0 10 6">
                                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.354.646a.5.5 0 0 0-.708 0L5 4.293 1.354.646a.5.5 0 0 0-.708.708l4 4a.5.5 0 0 0 .708 0l4-4a.5.5 0 0 0 0-.708z" fill="currentColor"/>
                                                </svg>
                                            </div>
                                        </summary>
                                        <div class="facets__display">
                                            <fieldset class="facets-wrap">
                                                <legend class="visually-hidden"><?= html_escape($filter['name']) ?></legend>
                                                <ul class="facets__list list-unstyled" role="list">
                                                    <?php 
                                                    $attribute_values = explode(',', $filter['attribute_values']);
                                                    foreach ($attribute_values as $value): 
                                                    ?>
                                                        <li class="list-menu__item facets__item">
                                                            <label for="Filter-<?= $key ?>-<?= $value ?>" class="facets__label">
                                                                <input type="checkbox" 
                                                                       name="filter-<?= strtolower(str_replace(' ', '-', $filter['name'])) ?>" 
                                                                       value="<?= strtolower($value) ?>" 
                                                                       id="Filter-<?= $key ?>-<?= $value ?>"
                                                                       class="facets__checkbox product_attributes">
                                                                <svg width="1.6rem" height="1.6rem" viewBox="0 0 16 16" aria-hidden="true" focusable="false">
                                                                    <rect width="16" height="16" stroke="currentColor" fill="none" stroke-width="1"></rect>
                                                                </svg>
                                                                <svg class="icon icon-checkmark" width="1.1rem" height="0.7rem" viewBox="0 0 11 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path d="m1.5 3.5 2.5 2.5 6-6" stroke="currentColor" stroke-width="1.75" fill="none"/>
                                                                </svg>
                                                                <span aria-hidden="true"><?= ucfirst($value) ?></span>
                                                            </label>
                                                        </li>
                                                    <?php endforeach; ?>
                                                </ul>
                                            </fieldset>
                                        </div>
                                    </details>
                                <?php endforeach; ?>
                            <?php endif; ?>

                            <!-- Brand Filters -->
                            <?php if (isset($brands) && !empty($brands)): ?>
                                <details class="disclosure-has-popup facets__disclosure js-filter" data-index="brands">
                                    <summary class="facets__summary">
                                        <div>
                                            <span>Brands</span>
                                            <svg aria-hidden="true" focusable="false" class="icon icon-caret" viewBox="0 0 10 6">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M9.354.646a.5.5 0 0 0-.708 0L5 4.293 1.354.646a.5.5 0 0 0-.708.708l4 4a.5.5 0 0 0 .708 0l4-4a.5.5 0 0 0 0-.708z" fill="currentColor"/>
                                            </svg>
                                        </div>
                                    </summary>
                                    <div class="facets__display">
                                        <fieldset class="facets-wrap">
                                            <legend class="visually-hidden">Brands</legend>
                                            <ul class="facets__list list-unstyled" role="list">
                                                <?php 
                                                $brands_filter = json_decode($brands, true);
                                                if ($brands_filter):
                                                    foreach ($brands_filter as $brand): 
                                                ?>
                                                    <li class="list-menu__item facets__item">
                                                        <label for="Filter-brand-<?= $brand['brand_id'] ?>" class="facets__label">
                                                            <input type="radio" 
                                                                   name="brand" 
                                                                   value="<?= $brand['brand_slug'] ?>" 
                                                                   id="Filter-brand-<?= $brand['brand_id'] ?>"
                                                                   class="facets__checkbox brand">
                                                            <svg width="1.6rem" height="1.6rem" viewBox="0 0 16 16" aria-hidden="true" focusable="false">
                                                                <circle cx="8" cy="8" r="7" stroke="currentColor" fill="none" stroke-width="1"></circle>
                                                            </svg>
                                                            <svg class="icon icon-checkmark" width="1.1rem" height="0.7rem" viewBox="0 0 11 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <circle cx="5.5" cy="3.5" r="2" fill="currentColor"/>
                                                            </svg>
                                                            <span aria-hidden="true"><?= $brand['brand_name'] ?></span>
                                                        </label>
                                                    </li>
                                                <?php 
                                                    endforeach; 
                                                endif;
                                                ?>
                                            </ul>
                                        </fieldset>
                                    </div>
                                </details>
                            <?php endif; ?>

                        </form>
                    </div>
                </details>
            </div>

            <!-- Products Grid -->
            <div class="product-grid-container">
                
                <!-- Toolbar -->
                <div class="facets-container">
                    <div class="facets__wrapper">
                        <div class="facets__header">
                            <span class="facets__selected">
                                <span id="ProductCountDesktop" class="product-count light">
                                    <?php if (isset($products['product']) && !empty($products['product'])): ?>
                                        <?= count($products['product']) ?> products
                                    <?php else: ?>
                                        0 products
                                    <?php endif; ?>
                                </span>
                            </span>
                        </div>
                        
                        <!-- Sort Options -->
                        <div class="facets__sort">
                            <div class="facet-filters sorting caption">
                                <div class="facet-filters__field">
                                    <h2 class="facet-filters__label caption-large text-body">
                                        <label for="SortBy">Sort by:</label>
                                    </h2>
                                    <div class="select">
                                        <select name="sort_by" class="facet-filters__sort select__select caption-large" id="SortBy" aria-describedby="a11y-refresh-page-message">
                                            <option value="manual">Featured</option>
                                            <option value="best-selling">Best selling</option>
                                            <option value="title-ascending">Alphabetically, A-Z</option>
                                            <option value="title-descending">Alphabetically, Z-A</option>
                                            <option value="price-ascending">Price, low to high</option>
                                            <option value="price-descending">Price, high to low</option>
                                            <option value="created-ascending">Date, old to new</option>
                                            <option value="created-descending">Date, new to old</option>
                                        </select>
                                        <svg aria-hidden="true" focusable="false" class="icon icon-caret" viewBox="0 0 10 6">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M9.354.646a.5.5 0 0 0-.708 0L5 4.293 1.354.646a.5.5 0 0 0-.708.708l4 4a.5.5 0 0 0 .708 0l4-4a.5.5 0 0 0 0-.708z" fill="currentColor"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Products Grid -->
                <div class="collection">
                    <div class="loading-overlay gradient"></div>
                    
                    <ul id="product-grid" class="grid product-grid grid--2-col-tablet-down grid--4-col-desktop" role="list">
                        <!-- Products will be loaded here dynamically -->
                    </ul>
                    
                    <!-- Loading State -->
                    <div id="products-loading" class="loading-container" style="display: none;">
                        <div class="loading-spinner"></div>
                        <p>Loading products...</p>
                    </div>
                    
                    <!-- No Products Message -->
                    <div id="no-products" class="collection--empty" style="display: none;">
                        <div class="collection--empty-content">
                            <h2>No products found</h2>
                            <p>Use fewer filters or clear all</p>
                            <a href="<?= base_url('products') ?>" class="button">
                                Clear all filters
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="pagination-wrapper">
                    <!-- Pagination will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Product Listing Styles -->
<style>
.section-product-listing {
    padding-top: 3rem;
    padding-bottom: 3rem;
}

.collection-hero {
    text-align: center;
    margin-bottom: 4rem;
}

.collection-hero__title {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--brand-black);
}

.collection-hero__description {
    font-size: 1.6rem;
    color: var(--brand-dark-gray);
    max-width: 60rem;
    margin: 0 auto;
}

.collection-product-wrapper {
    display: grid;
    grid-template-columns: 1fr;
    gap: 3rem;
}

@media screen and (min-width: 990px) {
    .collection-product-wrapper {
        grid-template-columns: 25rem 1fr;
    }
}

.facets-container {
    margin-bottom: 2rem;
}

.facets__wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 0;
    border-bottom: 1px solid var(--brand-light-gray);
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(25rem, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

@media screen and (min-width: 750px) {
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(30rem, 1fr));
    }
}

.loading-container {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem;
}

.collection--empty {
    grid-column: 1 / -1;
    text-align: center;
    padding: 5rem 2rem;
}

.collection--empty-content h2 {
    margin-bottom: 1rem;
    color: var(--brand-black);
}

.collection--empty-content p {
    margin-bottom: 2rem;
    color: var(--brand-dark-gray);
}

/* Filter Styles */
.facets__disclosure {
    border: 1px solid var(--brand-light-gray);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.facets__summary {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--brand-white);
}

.facets__summary:hover {
    background-color: var(--brand-off-white);
}

.facets__display {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--brand-light-gray);
}

.facets__list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.facets__item {
    margin-bottom: 0.8rem;
}

.facets__label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 1.4rem;
}

.facets__checkbox {
    margin-right: 1rem;
}

/* Responsive */
@media (max-width: 989px) {
    .collection-filters-wrapper {
        margin-bottom: 2rem;
    }
    
    .facets__form {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--brand-white);
        border: 1px solid var(--brand-light-gray);
        border-radius: 0.5rem;
        padding: 2rem;
        z-index: 10;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
}
</style>
