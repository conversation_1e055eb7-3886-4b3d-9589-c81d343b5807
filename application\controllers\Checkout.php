<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Checkout extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->database();
        $this->load->library(['ion_auth', 'form_validation', 'session']);
        $this->load->helper(['url', 'language', 'function_helper']);
        $this->load->model(['Order_model', 'Cart_model', 'Address_model']);
        
        // Check if user is logged in
        if (!$this->ion_auth->logged_in()) {
            redirect('customer-auth/login?redirect=checkout', 'refresh');
        }
        
        // Check if user is a customer
        if (!$this->ion_auth->in_group('customers')) {
            $this->ion_auth->logout();
            redirect('customer-auth/login', 'refresh');
        }
        
        $this->data['web_settings'] = get_settings('web_settings', true);
        $this->data['system_settings'] = get_settings('system_settings', true);
        $this->data['payment_settings'] = get_settings('payment_method', true);
        $this->data['user'] = $this->ion_auth->user()->row_array();
        $this->response['csrfName'] = $this->security->get_csrf_token_name();
        $this->response['csrfHash'] = $this->security->get_csrf_hash();
    }

    public function index()
    {
        // Check if cart has items
        $user_id = $this->data['user']['id'];
        $cart_items = $this->Cart_model->get_user_cart($user_id);
        
        if (empty($cart_items)) {
            redirect('cart', 'refresh');
        }

        $this->data['main_page'] = 'checkout';
        $this->data['title'] = 'Checkout | ' . $this->data['web_settings']['site_title'];
        $this->data['keywords'] = 'Checkout, ' . $this->data['web_settings']['meta_keywords'];
        $this->data['description'] = 'Checkout | ' . $this->data['web_settings']['meta_description'];
        $this->data['cart_items'] = $cart_items;
        
        $this->load->view('front-end/' . THEME . '/template', $this->data);
    }

    public function get_payment_methods()
    {
        $payment_methods = [];
        
        // Cash on Delivery
        if (isset($this->data['payment_settings']['cod_payment_method']) && 
            $this->data['payment_settings']['cod_payment_method'] == '1') {
            $payment_methods[] = [
                'id' => 'cod',
                'name' => 'Cash on Delivery',
                'description' => 'Pay when you receive your order',
                'type' => 'offline'
            ];
        }
        
        // Paystack
        if (isset($this->data['payment_settings']['paystack_payment_method']) && 
            $this->data['payment_settings']['paystack_payment_method'] == '1') {
            $payment_methods[] = [
                'id' => 'paystack',
                'name' => 'Paystack',
                'description' => 'Pay securely with card, bank transfer, or USSD',
                'type' => 'online'
            ];
        }
        
        // Flutterwave
        if (isset($this->data['payment_settings']['flutterwave_payment_method']) && 
            $this->data['payment_settings']['flutterwave_payment_method'] == '1') {
            $payment_methods[] = [
                'id' => 'flutterwave',
                'name' => 'Flutterwave',
                'description' => 'Pay with card, bank transfer, or mobile money',
                'type' => 'online'
            ];
        }
        
        // Bank Transfer
        $payment_methods[] = [
            'id' => 'bank_transfer',
            'name' => 'Bank Transfer',
            'description' => 'Direct bank transfer (manual verification)',
            'type' => 'offline'
        ];

        $this->response['error'] = false;
        $this->response['data'] = $payment_methods;
        echo json_encode($this->response);
    }

    public function place_order()
    {
        $this->form_validation->set_rules('shipping_info[first_name]', 'First Name', 'trim|required|xss_clean');
        $this->form_validation->set_rules('shipping_info[last_name]', 'Last Name', 'trim|required|xss_clean');
        $this->form_validation->set_rules('shipping_info[email]', 'Email', 'trim|required|valid_email|xss_clean');
        $this->form_validation->set_rules('shipping_info[mobile]', 'Mobile', 'trim|required|xss_clean');
        $this->form_validation->set_rules('shipping_info[address]', 'Address', 'trim|required|xss_clean');
        $this->form_validation->set_rules('shipping_info[city]', 'City', 'trim|required|xss_clean');
        $this->form_validation->set_rules('shipping_info[state]', 'State', 'trim|required|xss_clean');
        $this->form_validation->set_rules('shipping_info[pincode]', 'Pincode', 'trim|required|xss_clean');
        $this->form_validation->set_rules('payment_method', 'Payment Method', 'trim|required|xss_clean');

        if (!$this->form_validation->run()) {
            $this->response['error'] = true;
            $this->response['message'] = validation_errors();
            echo json_encode($this->response);
            return;
        }

        $user_id = $this->data['user']['id'];
        $shipping_info = $this->input->post('shipping_info', true);
        $payment_method = $this->input->post('payment_method', true);
        
        // Get cart items
        $cart_items = $this->Cart_model->get_user_cart($user_id);
        if (empty($cart_items)) {
            $this->response['error'] = true;
            $this->response['message'] = 'Your cart is empty';
            echo json_encode($this->response);
            return;
        }

        // Calculate totals
        $cart_total = get_cart_total($user_id);
        $subtotal = floatval($cart_total['overall_amount']);
        $shipping_charge = $this->calculate_shipping($shipping_info['pincode'], $subtotal);
        $tax_amount = $subtotal * 0.05; // 5% tax
        $final_total = $subtotal + $shipping_charge + $tax_amount;

        // Prepare order data
        $order_data = [
            'user_id' => $user_id,
            'total' => $subtotal,
            'delivery_charge' => $shipping_charge,
            'tax_amount' => $tax_amount,
            'tax_percentage' => 5,
            'wallet_balance' => 0,
            'final_total' => $final_total,
            'payment_method' => strtoupper($payment_method),
            'address' => json_encode($shipping_info),
            'delivery_time' => '',
            'delivery_date' => '',
            'order_note' => '',
            'date_added' => date('Y-m-d H:i:s'),
            'order_from' => 'web'
        ];

        // Place order using existing Order_model
        try {
            $order_result = $this->Order_model->place_order($order_data);
            
            if ($order_result['error'] === false) {
                $order_id = $order_result['order_id'];
                
                // Handle different payment methods
                if ($payment_method === 'cod') {
                    // For COD, order is placed successfully
                    $this->response['error'] = false;
                    $this->response['message'] = 'Order placed successfully!';
                    $this->response['order_id'] = $order_id;
                    $this->response['redirect'] = base_url('checkout/success?order_id=' . $order_id);
                    
                } elseif ($payment_method === 'paystack') {
                    // Initialize Paystack payment
                    $this->load->library('paystack');
                    $paystack_result = $this->paystack->initialize(
                        $final_total * 100, // Amount in kobo
                        $shipping_info['email'],
                        $order_id
                    );
                    
                    if ($paystack_result && isset($paystack_result['data']['authorization_url'])) {
                        $this->response['error'] = false;
                        $this->response['message'] = 'Redirecting to payment gateway...';
                        $this->response['order_id'] = $order_id;
                        $this->response['redirect_url'] = $paystack_result['data']['authorization_url'];
                    } else {
                        $this->response['error'] = true;
                        $this->response['message'] = 'Failed to initialize payment. Please try again.';
                    }
                    
                } elseif ($payment_method === 'flutterwave') {
                    // Initialize Flutterwave payment
                    $this->load->library('flutterwave');
                    $tx_ref = 'FLW_' . $order_id . '_' . time();
                    
                    $flutterwave_data = [
                        'tx_ref' => $tx_ref,
                        'amount' => $final_total,
                        'currency' => 'NGN',
                        'redirect_url' => base_url('payment/flutterwave_callback'),
                        'customer' => [
                            'email' => $shipping_info['email'],
                            'phonenumber' => $shipping_info['mobile'],
                            'name' => $shipping_info['first_name'] . ' ' . $shipping_info['last_name']
                        ],
                        'customizations' => [
                            'title' => $this->data['system_settings']['app_name'],
                            'description' => 'Payment for Order #' . $order_id,
                            'logo' => base_url(get_settings('logo'))
                        ]
                    ];
                    
                    $flutterwave_result = $this->flutterwave->initialize_payment($flutterwave_data);
                    
                    if ($flutterwave_result && isset($flutterwave_result['data']['link'])) {
                        $this->response['error'] = false;
                        $this->response['message'] = 'Redirecting to payment gateway...';
                        $this->response['order_id'] = $order_id;
                        $this->response['redirect_url'] = $flutterwave_result['data']['link'];
                    } else {
                        $this->response['error'] = true;
                        $this->response['message'] = 'Failed to initialize payment. Please try again.';
                    }
                    
                } else {
                    // For other payment methods (bank transfer, etc.)
                    $this->response['error'] = false;
                    $this->response['message'] = 'Order placed successfully! Please complete payment as instructed.';
                    $this->response['order_id'] = $order_id;
                    $this->response['redirect'] = base_url('checkout/success?order_id=' . $order_id);
                }
                
            } else {
                $this->response['error'] = true;
                $this->response['message'] = $order_result['message'];
            }
            
        } catch (Exception $e) {
            $this->response['error'] = true;
            $this->response['message'] = 'An error occurred while placing your order. Please try again.';
        }

        echo json_encode($this->response);
    }

    public function success()
    {
        $order_id = $this->input->get('order_id');
        
        if (!$order_id) {
            redirect('/', 'refresh');
        }
        
        // Verify order belongs to current user
        $user_id = $this->data['user']['id'];
        $order = $this->db->where(['id' => $order_id, 'user_id' => $user_id])
                         ->get('orders')
                         ->row_array();
        
        if (!$order) {
            redirect('/', 'refresh');
        }

        $this->data['main_page'] = 'checkout-success';
        $this->data['title'] = 'Order Successful | ' . $this->data['web_settings']['site_title'];
        $this->data['order'] = $order;
        
        $this->load->view('front-end/' . THEME . '/template', $this->data);
    }

    public function failed()
    {
        $this->data['main_page'] = 'checkout-failed';
        $this->data['title'] = 'Payment Failed | ' . $this->data['web_settings']['site_title'];
        
        $this->load->view('front-end/' . THEME . '/template', $this->data);
    }

    private function calculate_shipping($pincode, $subtotal)
    {
        // Simple shipping calculation
        // Free shipping for orders above ₦50,000
        if ($subtotal >= 50000) {
            return 0;
        }
        
        // Standard shipping rate
        return 2500; // ₦2,500
    }

    public function get_shipping_cost()
    {
        $pincode = $this->input->post('pincode');
        $subtotal = floatval($this->input->post('subtotal', true));
        
        $shipping_cost = $this->calculate_shipping($pincode, $subtotal);
        
        $this->response['error'] = false;
        $this->response['shipping_cost'] = $shipping_cost;
        $this->response['message'] = $shipping_cost > 0 ? 'Shipping: ₦' . number_format($shipping_cost) : 'Free shipping';
        
        echo json_encode($this->response);
    }
}
