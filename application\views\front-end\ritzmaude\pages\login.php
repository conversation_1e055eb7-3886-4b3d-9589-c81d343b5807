<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<!-- Login Page Content -->

<!-- <PERSON> Header -->
<section class="shopify-section section">
    <style>
        .page-header {
            padding-top: 42px;
            padding-bottom: 33px;
            text-align: center;
        }
        @media screen and (min-width: 750px) {
            .page-header {
                padding-top: 56px;
                padding-bottom: 44px;
            }
        }
    </style>
    <div class="page-header color-scheme-1 gradient">
        <div class="page-width">
            <h1 class="main-page-title page-title h0 scroll-trigger animate--fade-in">
                Account
            </h1>
        </div>
    </div>
</section>

<!-- Login Form Section -->
<section class="shopify-section section">
    <style>
        /* Black and Gold Color Scheme Variables */
        :root {
            --brand-black: #000000;
            --brand-white: #ffffff;
            --brand-gold: #D4AF37;
            --brand-gold-dark: #B8941F;
            --brand-light-gray: #f5f5f5;
            --primary-color: #D4AF37;
        }

        .section-login-padding {
            padding-top: 27px;
            padding-bottom: 27px;
        }
        @media screen and (min-width: 750px) {
            .section-login-padding {
                padding-top: 36px;
                padding-bottom: 36px;
            }
        }
        .login-form {
            max-width: 400px;
            margin: 0 auto;
            background: var(--brand-white);
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--brand-light-gray);
        }
        .field {
            margin-bottom: 1.5rem;
        }
        .field__input {
            width: 100%;
            padding: 1rem;
            border: 1px solid var(--brand-light-gray);
            border-radius: 4px;
            font-size: 1rem;
            background-color: var(--brand-white);
            color: var(--brand-black);
            transition: border-color 0.3s ease;
        }
        .field__input:focus {
            outline: none;
            border-color: var(--brand-gold);
            box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
        }
        .field__label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--brand-black);
        }
        .btn {
            background: var(--brand-gold);
            color: var(--brand-black);
            border: none;
            padding: 1rem 2rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            width: 100%;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: var(--brand-gold-dark);
            transform: translateY(-1px);
        }
        .btn--tertiary {
            background: transparent;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }
        .btn--tertiary:hover {
            background: var(--primary-color);
            color: white;
        }
        .form-tabs {
            display: flex;
            margin-bottom: 2rem;
            border-bottom: 1px solid rgba(var(--color-foreground), 0.1);
        }
        .form-tab {
            flex: 1;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }
        .form-tab.active {
            border-bottom-color: var(--primary-color);
            color: var(--primary-color);
            font-weight: 600;
        }
        .form-content {
            display: none;
        }
        .form-content.active {
            display: block;
        }
        .error-message {
            color: #d32f2f;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }
        .success-message {
            color: #2e7d32;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }
    </style>
    
    <div class="color-scheme-1 gradient">
        <div class="page-width isolate section-login-padding">
            <div class="login-form">
                
                <!-- Form Tabs -->
                <div class="form-tabs">
                    <div class="form-tab active" data-tab="login">Login</div>
                    <div class="form-tab" data-tab="register">Register</div>
                </div>

                <!-- Login Form -->
                <div class="form-content active" id="login-form">
                    <form action="<?= base_url('customer-auth/authenticate') ?>" method="post" id="customer-login-form">
                        <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">
                        
                        <div class="field">
                            <label class="field__label" for="customer-email">Email</label>
                            <input type="email" name="identity" id="customer-email" class="field__input" autocomplete="email" required>
                        </div>

                        <div class="field">
                            <label class="field__label" for="customer-password">Password</label>
                            <input type="password" name="password" id="customer-password" class="field__input" autocomplete="current-password" required>
                        </div>

                        <div class="field" style="margin-bottom: 2rem;">
                            <label style="display: flex; align-items: center; cursor: pointer;">
                                <input type="checkbox" name="remember" style="margin-right: 0.5rem;">
                                <span>Remember me</span>
                            </label>
                        </div>

                        <button type="submit" class="btn">Sign In</button>
                        
                        <div id="login-error" class="error-message" style="display: none;"></div>
                        <div id="login-success" class="success-message" style="display: none;"></div>
                    </form>

                    <div style="text-align: center; margin-top: 1.5rem;">
                        <a href="#" onclick="showForgotPassword()" style="color: var(--primary-color); text-decoration: none;">
                            Forgot your password?
                        </a>
                    </div>
                </div>

                <!-- Register Form -->
                <div class="form-content" id="register-form">
                    <form action="<?= base_url('customer-auth/create-account') ?>" method="post" id="customer-register-form">
                        <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">
                        
                        <div class="field">
                            <label class="field__label" for="register-first-name">First Name</label>
                            <input type="text" name="first_name" id="register-first-name" class="field__input" autocomplete="given-name" required>
                        </div>

                        <div class="field">
                            <label class="field__label" for="register-last-name">Last Name</label>
                            <input type="text" name="last_name" id="register-last-name" class="field__input" autocomplete="family-name" required>
                        </div>

                        <div class="field">
                            <label class="field__label" for="register-email">Email</label>
                            <input type="email" name="email" id="register-email" class="field__input" autocomplete="email" required>
                        </div>

                        <div class="field">
                            <label class="field__label" for="register-mobile">Mobile Number</label>
                            <input type="tel" name="mobile" id="register-mobile" class="field__input" autocomplete="tel" required>
                        </div>

                        <div class="field">
                            <label class="field__label" for="register-password">Password</label>
                            <input type="password" name="password" id="register-password" class="field__input" autocomplete="new-password" required>
                        </div>

                        <div class="field">
                            <label class="field__label" for="register-confirm-password">Confirm Password</label>
                            <input type="password" name="confirm_password" id="register-confirm-password" class="field__input" autocomplete="new-password" required>
                        </div>

                        <div class="field" style="margin-bottom: 2rem;">
                            <label style="display: flex; align-items: flex-start; cursor: pointer;">
                                <input type="checkbox" name="terms" style="margin-right: 0.5rem; margin-top: 0.25rem;" required>
                                <span style="font-size: 0.875rem;">
                                    I agree to the <a href="<?= base_url('terms-and-conditions') ?>" style="color: var(--primary-color);">Terms & Conditions</a> 
                                    and <a href="<?= base_url('privacy-policy') ?>" style="color: var(--primary-color);">Privacy Policy</a>
                                </span>
                            </label>
                        </div>

                        <button type="submit" class="btn">Create Account</button>
                        
                        <div id="register-error" class="error-message" style="display: none;"></div>
                        <div id="register-success" class="success-message" style="display: none;"></div>
                    </form>
                </div>

                <!-- Forgot Password Form -->
                <div class="form-content" id="forgot-password-form">
                    <form action="<?= base_url('customer-auth/forgot-password') ?>" method="post" id="customer-forgot-form">
                        <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">
                        
                        <h3 style="margin-bottom: 1rem;">Reset Password</h3>
                        <p style="margin-bottom: 1.5rem; color: rgba(var(--color-foreground), 0.7);">
                            Enter your email address and we'll send you a link to reset your password.
                        </p>
                        
                        <div class="field">
                            <label class="field__label" for="forgot-email">Email</label>
                            <input type="email" name="email" id="forgot-email" class="field__input" autocomplete="email" required>
                        </div>

                        <button type="submit" class="btn">Send Reset Link</button>
                        
                        <div id="forgot-error" class="error-message" style="display: none;"></div>
                        <div id="forgot-success" class="success-message" style="display: none;"></div>
                    </form>

                    <div style="text-align: center; margin-top: 1.5rem;">
                        <a href="#" onclick="showLogin()" style="color: var(--primary-color); text-decoration: none;">
                            Back to Login
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- JavaScript for Login/Register -->
<script>
function initializeAuthForms() {
    setupTabSwitching();
    setupFormSubmissions();
}

function setupTabSwitching() {
    $('.form-tab').on('click', function() {
        var tab = $(this).data('tab');
        
        // Update active tab
        $('.form-tab').removeClass('active');
        $(this).addClass('active');
        
        // Show corresponding form
        $('.form-content').removeClass('active');
        $('#' + tab + '-form').addClass('active');
    });
}

function setupFormSubmissions() {
    // Login form
    $('#customer-login-form').on('submit', function(e) {
        e.preventDefault();
        
        var formData = $(this).serialize();
        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.text();
        
        submitBtn.prop('disabled', true).text('Signing in...');
        $('#login-error').hide();
        $('#login-success').hide();
        
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.error === false) {
                    $('#login-success').text('Login successful! Redirecting...').show();
                    setTimeout(function() {
                        var redirect = new URLSearchParams(window.location.search).get('redirect');
                        window.location.href = redirect ? window.eShop.baseUrl + redirect : window.eShop.baseUrl + 'my-account';
                    }, 1000);
                } else {
                    $('#login-error').text(response.message || 'Login failed. Please try again.').show();
                }
            },
            error: function() {
                $('#login-error').text('An error occurred. Please try again.').show();
            },
            complete: function() {
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });
    
    // Register form
    $('#customer-register-form').on('submit', function(e) {
        e.preventDefault();
        
        // Validate password confirmation
        var password = $('#register-password').val();
        var confirmPassword = $('#register-confirm-password').val();
        
        if (password !== confirmPassword) {
            $('#register-error').text('Passwords do not match.').show();
            return;
        }
        
        var formData = $(this).serialize();
        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.text();
        
        submitBtn.prop('disabled', true).text('Creating account...');
        $('#register-error').hide();
        $('#register-success').hide();
        
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.error === false) {
                    $('#register-success').text('Account created successfully! Please check your email for verification.').show();
                    setTimeout(function() {
                        showLogin();
                    }, 2000);
                } else {
                    $('#register-error').text(response.message || 'Registration failed. Please try again.').show();
                }
            },
            error: function() {
                $('#register-error').text('An error occurred. Please try again.').show();
            },
            complete: function() {
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });
    
    // Forgot password form
    $('#customer-forgot-form').on('submit', function(e) {
        e.preventDefault();
        
        var formData = $(this).serialize();
        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.text();
        
        submitBtn.prop('disabled', true).text('Sending...');
        $('#forgot-error').hide();
        $('#forgot-success').hide();
        
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.error === false) {
                    $('#forgot-success').text('Reset link sent! Please check your email.').show();
                    setTimeout(function() {
                        showLogin();
                    }, 3000);
                } else {
                    $('#forgot-error').text(response.message || 'Failed to send reset link. Please try again.').show();
                }
            },
            error: function() {
                $('#forgot-error').text('An error occurred. Please try again.').show();
            },
            complete: function() {
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });
}

function showLogin() {
    $('.form-tab').removeClass('active');
    $('.form-tab[data-tab="login"]').addClass('active');
    $('.form-content').removeClass('active');
    $('#login-form').addClass('active');
}

function showRegister() {
    $('.form-tab').removeClass('active');
    $('.form-tab[data-tab="register"]').addClass('active');
    $('.form-content').removeClass('active');
    $('#register-form').addClass('active');
}

function showForgotPassword() {
    $('.form-content').removeClass('active');
    $('#forgot-password-form').addClass('active');
}

// Initialize when document is ready
$(document).ready(function() {
    if (typeof initializeDynamicContent === 'function') {
        initializeDynamicContent();
    }
    initializeAuthForms();
});
</script>
