<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<!-- Checkout Page Content -->

<!-- <PERSON> Header -->
<section class="shopify-section section">
    <style>
        .page-header {
            padding-top: 42px;
            padding-bottom: 33px;
            text-align: center;
        }
        @media screen and (min-width: 750px) {
            .page-header {
                padding-top: 56px;
                padding-bottom: 44px;
            }
        }
    </style>
    <div class="page-header color-scheme-1 gradient">
        <div class="page-width">
            <h1 class="main-page-title page-title h0 scroll-trigger animate--fade-in">
                Checkout
            </h1>
        </div>
    </div>
</section>

<!-- Checkout Content -->
<section class="shopify-section section">
    <style>
        .section-checkout-padding {
            padding-top: 27px;
            padding-bottom: 27px;
        }
        @media screen and (min-width: 750px) {
            .section-checkout-padding {
                padding-top: 36px;
                padding-bottom: 44px;
            }
        }
        .checkout-step {
            background: rgba(var(--color-foreground), 0.02);
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        .checkout-step h3 {
            margin: 0 0 1.5rem 0;
            color: var(--primary-color);
        }
        .form-group {
            margin-bottom: 1.5rem;
        }
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid rgba(var(--color-foreground), 0.2);
            border-radius: 4px;
            font-size: 1rem;
        }
        .form-select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid rgba(var(--color-foreground), 0.2);
            border-radius: 4px;
            font-size: 1rem;
            background: white;
        }
        .btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.3s ease;
        }
        .btn:hover {
            background: var(--secondary-color);
        }
        .btn-full {
            width: 100%;
        }
        .order-summary {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            border: 1px solid rgba(var(--color-foreground), 0.1);
            position: sticky;
            top: 2rem;
        }
        .order-item {
            display: flex;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid rgba(var(--color-foreground), 0.1);
        }
        .order-item:last-child {
            border-bottom: none;
        }
        .order-item img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
            margin-right: 1rem;
        }
        .order-item-details {
            flex: 1;
        }
        .order-item-name {
            font-weight: 500;
            margin-bottom: 0.25rem;
        }
        .order-item-price {
            color: rgba(var(--color-foreground), 0.7);
        }
        .order-total-line {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
        }
        .order-total-line.total {
            font-weight: 600;
            font-size: 1.1rem;
            border-top: 1px solid rgba(var(--color-foreground), 0.1);
            margin-top: 1rem;
            padding-top: 1rem;
        }
        .payment-method {
            border: 1px solid rgba(var(--color-foreground), 0.2);
            border-radius: 4px;
            padding: 1rem;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .payment-method:hover {
            border-color: var(--primary-color);
        }
        .payment-method.selected {
            border-color: var(--primary-color);
            background: rgba(var(--primary-color), 0.05);
        }
        .payment-method input[type="radio"] {
            margin-right: 0.75rem;
        }
        .checkout-steps {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
            padding: 0;
            list-style: none;
        }
        .checkout-steps li {
            display: flex;
            align-items: center;
            color: rgba(var(--color-foreground), 0.5);
        }
        .checkout-steps li.active {
            color: var(--primary-color);
            font-weight: 600;
        }
        .checkout-steps li.completed {
            color: var(--primary-color);
        }
        .checkout-steps li:not(:last-child)::after {
            content: '→';
            margin: 0 1rem;
            color: rgba(var(--color-foreground), 0.3);
        }
    </style>
    
    <div class="color-scheme-1 gradient">
        <div class="page-width isolate section-checkout-padding">
            
            <!-- Checkout Steps -->
            <ul class="checkout-steps">
                <li class="active" id="step-shipping">Shipping</li>
                <li id="step-payment">Payment</li>
                <li id="step-review">Review</li>
            </ul>

            <div class="grid grid--1-col grid--2-col-tablet">
                
                <!-- Checkout Form -->
                <div class="grid__item">
                    <form id="checkout-form">
                        <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">
                        
                        <!-- Shipping Information -->
                        <div class="checkout-step" id="shipping-step">
                            <h3>Shipping Information</h3>
                            
                            <div class="grid grid--1-col grid--2-col-tablet" style="gap: 1rem;">
                                <div class="form-group">
                                    <label class="form-label" for="first-name">First Name *</label>
                                    <input type="text" id="first-name" name="first_name" class="form-input" required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label" for="last-name">Last Name *</label>
                                    <input type="text" id="last-name" name="last_name" class="form-input" required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="email">Email *</label>
                                <input type="email" id="email" name="email" class="form-input" required>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="mobile">Mobile Number *</label>
                                <input type="tel" id="mobile" name="mobile" class="form-input" required>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="address">Address *</label>
                                <textarea id="address" name="address" class="form-input" rows="3" required></textarea>
                            </div>
                            
                            <div class="grid grid--1-col grid--3-col-tablet" style="gap: 1rem;">
                                <div class="form-group">
                                    <label class="form-label" for="city">City *</label>
                                    <input type="text" id="city" name="city" class="form-input" required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label" for="state">State *</label>
                                    <select id="state" name="state" class="form-select" required>
                                        <option value="">Select State</option>
                                        <!-- States will be loaded dynamically -->
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label" for="pincode">Pincode *</label>
                                    <input type="text" id="pincode" name="pincode" class="form-input" required>
                                </div>
                            </div>
                            
                            <button type="button" class="btn btn-full" onclick="proceedToPayment()">Continue to Payment</button>
                        </div>

                        <!-- Payment Information -->
                        <div class="checkout-step" id="payment-step" style="display: none;">
                            <h3>Payment Method</h3>
                            
                            <div id="payment-methods">
                                <!-- Payment methods will be loaded dynamically -->
                            </div>
                            
                            <div style="display: flex; gap: 1rem;">
                                <button type="button" class="btn" style="background: #6c757d;" onclick="backToShipping()">Back</button>
                                <button type="button" class="btn btn-full" onclick="proceedToReview()">Continue to Review</button>
                            </div>
                        </div>

                        <!-- Order Review -->
                        <div class="checkout-step" id="review-step" style="display: none;">
                            <h3>Review Your Order</h3>
                            
                            <div id="review-details">
                                <!-- Order review details will be populated here -->
                            </div>
                            
                            <div class="form-group">
                                <label style="display: flex; align-items: center; cursor: pointer;">
                                    <input type="checkbox" name="terms" required style="margin-right: 0.5rem;">
                                    <span>I agree to the <a href="<?= base_url('terms-and-conditions') ?>" target="_blank" style="color: var(--primary-color);">Terms & Conditions</a></span>
                                </label>
                            </div>
                            
                            <div style="display: flex; gap: 1rem;">
                                <button type="button" class="btn" style="background: #6c757d;" onclick="backToPayment()">Back</button>
                                <button type="submit" class="btn btn-full" id="place-order-btn">Place Order</button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Order Summary -->
                <div class="grid__item">
                    <div class="order-summary">
                        <h3 style="margin: 0 0 1.5rem 0;">Order Summary</h3>
                        
                        <div id="checkout-items">
                            <!-- Cart items will be loaded here -->
                        </div>
                        
                        <div style="margin-top: 1.5rem;">
                            <div class="order-total-line">
                                <span>Subtotal</span>
                                <span id="checkout-subtotal">₦0.00</span>
                            </div>
                            <div class="order-total-line">
                                <span>Shipping</span>
                                <span id="checkout-shipping">Calculated at next step</span>
                            </div>
                            <div class="order-total-line">
                                <span>Tax</span>
                                <span id="checkout-tax">₦0.00</span>
                            </div>
                            <div class="order-total-line total">
                                <span>Total</span>
                                <span id="checkout-total">₦0.00</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Payment Gateway Scripts -->
<script src="https://js.paystack.co/v1/inline.js"></script>
<script src="https://checkout.flutterwave.com/v3.js"></script>

<!-- JavaScript for Checkout -->
<script>
// Global checkout data
window.checkoutData = {
    cartItems: [],
    subtotal: 0,
    shipping: 0,
    tax: 0,
    total: 0,
    currentStep: 'shipping',
    shippingInfo: {},
    paymentMethod: '',
    paymentMethods: []
};

function initializeCheckout() {
    loadCartItems();
    loadPaymentMethods();
    loadStates();
    setupFormValidation();
}

function loadCartItems() {
    $.ajax({
        url: window.eShop.apiUrl + 'get_user_cart',
        type: 'POST',
        data: {
            [window.eShop.csrfName]: window.eShop.csrfHash
        },
        success: function(response) {
            if (response.error === false && response.data && response.data.length > 0) {
                window.checkoutData.cartItems = response.data;
                renderCheckoutItems(response.data);
                calculateTotals();
            } else {
                // Redirect to cart if empty
                window.location.href = window.eShop.baseUrl + 'cart';
            }
        },
        error: function() {
            window.location.href = window.eShop.baseUrl + 'cart';
        }
    });
}

function renderCheckoutItems(items) {
    var html = '';
    
    items.forEach(function(item) {
        var imageUrl = item.image ? window.eShop.baseUrl + item.image : window.eShop.baseUrl + 'assets/no-image.png';
        var price = window.eShop.currencySymbol + parseFloat(item.price).toLocaleString();
        var totalPrice = window.eShop.currencySymbol + (parseFloat(item.price) * parseInt(item.qty)).toLocaleString();
        
        html += '<div class="order-item">';
        html += '<img src="' + imageUrl + '" alt="' + item.name + '">';
        html += '<div class="order-item-details">';
        html += '<div class="order-item-name">' + item.name + '</div>';
        if (item.measurement) {
            html += '<div class="order-item-price">Size: ' + item.measurement + '</div>';
        }
        html += '<div class="order-item-price">Qty: ' + item.qty + ' × ' + price + '</div>';
        html += '</div>';
        html += '<div style="font-weight: 600;">' + totalPrice + '</div>';
        html += '</div>';
    });
    
    $('#checkout-items').html(html);
}

function calculateTotals() {
    var subtotal = 0;
    
    window.checkoutData.cartItems.forEach(function(item) {
        subtotal += parseFloat(item.price) * parseInt(item.qty);
    });
    
    window.checkoutData.subtotal = subtotal;
    window.checkoutData.tax = subtotal * 0.05; // 5% tax
    window.checkoutData.total = subtotal + window.checkoutData.shipping + window.checkoutData.tax;
    
    // Update UI
    $('#checkout-subtotal').text(window.eShop.currencySymbol + subtotal.toLocaleString());
    $('#checkout-tax').text(window.eShop.currencySymbol + window.checkoutData.tax.toLocaleString());
    $('#checkout-total').text(window.eShop.currencySymbol + window.checkoutData.total.toLocaleString());
}

function loadPaymentMethods() {
    $.ajax({
        url: window.eShop.baseUrl + 'checkout/get_payment_methods',
        type: 'POST',
        data: {
            [window.eShop.csrfName]: window.eShop.csrfHash
        },
        success: function(response) {
            if (response.error === false && response.data) {
                window.checkoutData.paymentMethods = response.data;
                renderPaymentMethods(response.data);
            } else {
                // Default payment methods
                var defaultMethods = [
                    {id: 'cod', name: 'Cash on Delivery', description: 'Pay when you receive your order', type: 'offline'},
                    {id: 'paystack', name: 'Paystack', description: 'Pay securely with card, bank transfer, or USSD', type: 'online'},
                    {id: 'bank_transfer', name: 'Bank Transfer', description: 'Direct bank transfer', type: 'offline'}
                ];
                renderPaymentMethods(defaultMethods);
            }
        }
    });
}

function renderPaymentMethods(methods) {
    var html = '';
    
    methods.forEach(function(method, index) {
        var checked = index === 0 ? 'checked' : '';
        html += '<div class="payment-method ' + (index === 0 ? 'selected' : '') + '" onclick="selectPaymentMethod(\'' + method.id + '\')">';
        html += '<label style="cursor: pointer; display: flex; align-items: center;">';
        html += '<input type="radio" name="payment_method" value="' + method.id + '" ' + checked + '>';
        html += '<div>';
        html += '<strong>' + method.name + '</strong>';
        if (method.description) {
            html += '<br><small style="color: rgba(var(--color-foreground), 0.7);">' + method.description + '</small>';
        }
        html += '</div>';
        html += '</label>';
        html += '</div>';
    });
    
    $('#payment-methods').html(html);
    
    if (methods.length > 0) {
        window.checkoutData.paymentMethod = methods[0].id;
    }
}

function loadStates() {
    // Load states/provinces
    var states = [
        'Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa', 'Benue', 'Borno', 'Cross River', 'Delta',
        'Ebonyi', 'Edo', 'Ekiti', 'Enugu', 'FCT', 'Gombe', 'Imo', 'Jigawa', 'Kaduna', 'Kano', 'Katsina', 'Kebbi',
        'Kogi', 'Kwara', 'Lagos', 'Nasarawa', 'Niger', 'Ogun', 'Ondo', 'Osun', 'Oyo', 'Plateau', 'Rivers',
        'Sokoto', 'Taraba', 'Yobe', 'Zamfara'
    ];
    
    var html = '<option value="">Select State</option>';
    states.forEach(function(state) {
        html += '<option value="' + state + '">' + state + '</option>';
    });
    
    $('#state').html(html);
}

function selectPaymentMethod(methodId) {
    window.checkoutData.paymentMethod = methodId;
    
    $('.payment-method').removeClass('selected');
    $('.payment-method input[value="' + methodId + '"]').closest('.payment-method').addClass('selected');
    $('input[name="payment_method"][value="' + methodId + '"]').prop('checked', true);
}

function proceedToPayment() {
    if (validateShippingForm()) {
        // Save shipping info
        window.checkoutData.shippingInfo = {
            first_name: $('#first-name').val(),
            last_name: $('#last-name').val(),
            email: $('#email').val(),
            mobile: $('#mobile').val(),
            address: $('#address').val(),
            city: $('#city').val(),
            state: $('#state').val(),
            pincode: $('#pincode').val()
        };
        
        // Calculate shipping
        calculateShipping();
        
        // Update steps
        updateCheckoutStep('payment');
    }
}

function proceedToReview() {
    if (window.checkoutData.paymentMethod) {
        generateOrderReview();
        updateCheckoutStep('review');
    } else {
        alert('Please select a payment method');
    }
}

function backToShipping() {
    updateCheckoutStep('shipping');
}

function backToPayment() {
    updateCheckoutStep('payment');
}

function updateCheckoutStep(step) {
    window.checkoutData.currentStep = step;
    
    // Update step indicators
    $('.checkout-steps li').removeClass('active completed');
    
    if (step === 'shipping') {
        $('#step-shipping').addClass('active');
    } else if (step === 'payment') {
        $('#step-shipping').addClass('completed');
        $('#step-payment').addClass('active');
    } else if (step === 'review') {
        $('#step-shipping, #step-payment').addClass('completed');
        $('#step-review').addClass('active');
    }
    
    // Show/hide step content
    $('.checkout-step').hide();
    $('#' + step + '-step').show();
}

function validateShippingForm() {
    var required = ['first-name', 'last-name', 'email', 'mobile', 'address', 'city', 'state', 'pincode'];
    var isValid = true;
    
    required.forEach(function(field) {
        var value = $('#' + field).val().trim();
        if (!value) {
            $('#' + field).css('border-color', '#dc3545');
            isValid = false;
        } else {
            $('#' + field).css('border-color', '');
        }
    });
    
    if (!isValid) {
        alert('Please fill in all required fields');
    }
    
    return isValid;
}

function calculateShipping() {
    var pincode = $('#pincode').val();

    if (!pincode) {
        return;
    }

    $.ajax({
        url: window.eShop.baseUrl + 'checkout/get_shipping_cost',
        type: 'POST',
        data: {
            [window.eShop.csrfName]: window.eShop.csrfHash,
            pincode: pincode,
            subtotal: window.checkoutData.subtotal
        },
        dataType: 'json',
        success: function(response) {
            if (response.error === false) {
                window.checkoutData.shipping = response.shipping_cost;
                $('#checkout-shipping').text(response.shipping_cost > 0 ? '₦' + response.shipping_cost.toLocaleString() : 'Free');
                calculateTotals();
            }
        }
    });
}

function generateOrderReview() {
    var html = '<div style="background: rgba(var(--color-foreground), 0.02); padding: 1.5rem; border-radius: 4px; margin-bottom: 1.5rem;">';
    html += '<h4>Shipping Address</h4>';
    html += '<p>' + window.checkoutData.shippingInfo.first_name + ' ' + window.checkoutData.shippingInfo.last_name + '<br>';
    html += window.checkoutData.shippingInfo.address + '<br>';
    html += window.checkoutData.shippingInfo.city + ', ' + window.checkoutData.shippingInfo.state + ' ' + window.checkoutData.shippingInfo.pincode + '<br>';
    html += 'Phone: ' + window.checkoutData.shippingInfo.mobile + '</p>';
    html += '</div>';
    
    html += '<div style="background: rgba(var(--color-foreground), 0.02); padding: 1.5rem; border-radius: 4px;">';
    html += '<h4>Payment Method</h4>';
    var selectedMethod = window.checkoutData.paymentMethods.find(m => m.id === window.checkoutData.paymentMethod);
    html += '<p>' + (selectedMethod ? selectedMethod.name : window.checkoutData.paymentMethod) + '</p>';
    html += '</div>';
    
    $('#review-details').html(html);
}

function setupFormValidation() {
    $('#checkout-form').on('submit', function(e) {
        e.preventDefault();
        
        if (window.checkoutData.currentStep === 'review') {
            placeOrder();
        }
    });
}

function placeOrder() {
    var submitBtn = $('#place-order-btn');
    var originalText = submitBtn.text();
    
    submitBtn.prop('disabled', true).text('Placing Order...');
    
    var orderData = {
        [window.eShop.csrfName]: window.eShop.csrfHash,
        shipping_info: window.checkoutData.shippingInfo,
        payment_method: window.checkoutData.paymentMethod,
        items: window.checkoutData.cartItems,
        subtotal: window.checkoutData.subtotal,
        shipping: window.checkoutData.shipping,
        tax: window.checkoutData.tax,
        total: window.checkoutData.total
    };
    
    $.ajax({
        url: window.eShop.baseUrl + 'checkout/place_order',
        type: 'POST',
        data: orderData,
        dataType: 'json',
        success: function(response) {
            if (response.error === false) {
                // Handle different payment methods
                if (response.redirect_url) {
                    // For online payments (Paystack, Flutterwave, etc.)
                    window.location.href = response.redirect_url;
                } else if (response.redirect) {
                    // For offline payments (COD, Bank Transfer)
                    window.location.href = response.redirect;
                } else {
                    // Fallback
                    window.location.href = window.eShop.baseUrl + 'checkout/success?order_id=' + response.order_id;
                }
            } else {
                window.eShop.cart.showNotification(response.message || 'Failed to place order. Please try again.', 'error');
                submitBtn.prop('disabled', false).text(originalText);
            }
        },
        error: function() {
            window.eShop.cart.showNotification('An error occurred. Please try again.', 'error');
            submitBtn.prop('disabled', false).text(originalText);
        }
    });
}

// Initialize when document is ready
$(document).ready(function() {
    if (typeof initializeDynamicContent === 'function') {
        initializeDynamicContent();
    }
    initializeCheckout();
});
</script>
