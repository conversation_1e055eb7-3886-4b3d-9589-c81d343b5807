<?php
/**
 * Add Sample Products for Ritz Maude
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'ritzm';

try {
    // Create connection
    $conn = new mysqli($host, $username, $password, $database);
    
    // Check connection
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    
    echo "<h2>Adding Sample Products for Ritz Maude...</h2>";
    
    // Get category IDs
    $categories = [];
    $cat_result = $conn->query("SELECT id, name, slug FROM categories");
    if ($cat_result) {
        while ($row = $cat_result->fetch_assoc()) {
            $categories[$row['slug']] = $row['id'];
        }
    }
    
    // Sample products data
    $sample_products = [
        [
            'name' => 'Gracefully Broken Co-ord Set',
            'slug' => 'gracefully-broken-coord-set',
            'description' => 'A beautifully crafted co-ord set from our Gracefully Broken collection. Made with premium materials for comfort and style.',
            'short_description' => 'Premium co-ord set from Gracefully Broken collection',
            'price' => 45000.00,
            'sale_price' => 38000.00,
            'sku' => 'RM-GB-001',
            'category_slug' => 'co-ords',
            'image' => 'assets/front_end/Ritz Maude Frontpage/images/files-ANDREW_TABITHA_2025_034.jpg',
            'featured' => 1
        ],
        [
            'name' => 'Men\'s Classic Shirt',
            'slug' => 'mens-classic-shirt',
            'description' => 'A timeless classic shirt for the modern man. Perfect for both casual and formal occasions.',
            'short_description' => 'Classic men\'s shirt for any occasion',
            'price' => 25000.00,
            'sale_price' => null,
            'sku' => 'RM-MS-001',
            'category_slug' => 'shirts',
            'image' => 'assets/front_end/Ritz Maude Frontpage/images/collections-AXR_5999crop2_1.jpg',
            'featured' => 1
        ],
        [
            'name' => 'Women\'s Elegant Dress',
            'slug' => 'womens-elegant-dress',
            'description' => 'An elegant dress that combines comfort with sophisticated style. Perfect for special occasions.',
            'short_description' => 'Elegant dress for special occasions',
            'price' => 35000.00,
            'sale_price' => 30000.00,
            'sku' => 'RM-WD-001',
            'category_slug' => 'dresses',
            'image' => 'assets/front_end/Ritz Maude Frontpage/images/collections-AXR_6000crop2_1.jpg',
            'featured' => 1
        ],
        [
            'name' => 'Casual Shorts',
            'slug' => 'casual-shorts',
            'description' => 'Comfortable casual shorts perfect for everyday wear. Made with breathable fabric.',
            'short_description' => 'Comfortable casual shorts',
            'price' => 15000.00,
            'sale_price' => null,
            'sku' => 'RM-CS-001',
            'category_slug' => 'shorts',
            'image' => 'assets/front_end/Ritz Maude Frontpage/images/collections-AXR_5999crop2_1.jpg',
            'featured' => 0
        ],
        [
            'name' => 'Women\'s Jumpsuit',
            'slug' => 'womens-jumpsuit',
            'description' => 'A stylish jumpsuit that offers both comfort and elegance. Perfect for day or night wear.',
            'short_description' => 'Stylish and comfortable jumpsuit',
            'price' => 40000.00,
            'sale_price' => 35000.00,
            'sku' => 'RM-WJ-001',
            'category_slug' => 'jumpsuits',
            'image' => 'assets/front_end/Ritz Maude Frontpage/images/collections-AXR_6000crop2_1.jpg',
            'featured' => 1
        ],
        [
            'name' => 'Men\'s Trousers',
            'slug' => 'mens-trousers',
            'description' => 'Well-tailored trousers for the modern gentleman. Suitable for both business and casual wear.',
            'short_description' => 'Well-tailored men\'s trousers',
            'price' => 30000.00,
            'sale_price' => null,
            'sku' => 'RM-MT-001',
            'category_slug' => 'trousers',
            'image' => 'assets/front_end/Ritz Maude Frontpage/images/collections-AXR_5999crop2_1.jpg',
            'featured' => 0
        ]
    ];
    
    foreach ($sample_products as $product) {
        // Check if product already exists
        $check_sql = "SELECT id FROM products WHERE slug = '" . $conn->real_escape_string($product['slug']) . "'";
        $result = $conn->query($check_sql);
        
        if ($result->num_rows == 0) {
            $category_id = isset($categories[$product['category_slug']]) ? $categories[$product['category_slug']] : 1;
            
            $insert_sql = "INSERT INTO products (
                name, slug, description, short_description, price, sale_price, sku, 
                stock_quantity, manage_stock, in_stock, category_id, image, 
                status, featured, created_at, updated_at
            ) VALUES (
                '" . $conn->real_escape_string($product['name']) . "',
                '" . $conn->real_escape_string($product['slug']) . "',
                '" . $conn->real_escape_string($product['description']) . "',
                '" . $conn->real_escape_string($product['short_description']) . "',
                " . $product['price'] . ",
                " . ($product['sale_price'] ? $product['sale_price'] : 'NULL') . ",
                '" . $conn->real_escape_string($product['sku']) . "',
                50,
                1,
                1,
                " . $category_id . ",
                '" . $conn->real_escape_string($product['image']) . "',
                1,
                " . $product['featured'] . ",
                NOW(),
                NOW()
            )";
            
            if ($conn->query($insert_sql) === TRUE) {
                echo "✅ Added product: " . $product['name'] . "<br>";
            } else {
                echo "❌ Error adding product " . $product['name'] . ": " . $conn->error . "<br>";
            }
        } else {
            echo "ℹ️ Product already exists: " . $product['name'] . "<br>";
        }
    }
    
    echo "<h3>Sample products added successfully!</h3>";
    echo "<p><a href='check_database_status.php'>Check Database Status</a> | <a href='index.php'>Go to Website</a></p>";
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<h2>Database Error:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>
